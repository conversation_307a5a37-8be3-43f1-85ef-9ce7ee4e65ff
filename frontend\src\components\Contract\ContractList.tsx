import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Grid,
  Alert,
  Fab,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  MoreVert,
  Edit,
  Delete,
  Visibility,
  Add,
  Search,
  FilterList,
  Download,
  Send,
  CheckCircle,
  Cancel,
  Warning,
  Mic
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { contractApi } from '../../services/api';

interface Contract {
  id: number;
  uuid: string;
  contractNumber: string;
  customerName: string;
  amount: number;
  status: string;
  createdAt: string;
  updatedAt?: string;
}

const ContractList: React.FC = () => {
  const navigate = useNavigate();
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedContract, setSelectedContract] = useState<Contract | null>(null);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [detailDialog, setDetailDialog] = useState(false);
  const [contractDetail, setContractDetail] = useState<any>(null);

  useEffect(() => {
    loadContracts();
  }, [page, rowsPerPage, searchTerm, statusFilter]);

  const loadContracts = async () => {
    setLoading(true);
    try {
      const response = await contractApi.getContractList({
        page: page + 1,
        size: rowsPerPage,
        status: statusFilter || undefined,
        customer_name: searchTerm || undefined
      });

      if (response.success) {
        setContracts(response.data.contracts);
        setTotal(response.data.total);
      }
    } catch (error) {
      console.error('Failed to load contracts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, contract: Contract) => {
    setAnchorEl(event.currentTarget);
    setSelectedContract(contract);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedContract(null);
  };

  const handleViewDetail = async () => {
    if (!selectedContract) return;
    
    try {
      const response = await contractApi.getContract(selectedContract.uuid);
      if (response.success) {
        setContractDetail(response.data);
        setDetailDialog(true);
      }
    } catch (error) {
      console.error('Failed to load contract detail:', error);
    }
    handleMenuClose();
  };

  const handleEdit = () => {
    if (selectedContract) {
      navigate(`/contracts/edit/${selectedContract.uuid}`);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    setDeleteDialog(true);
    handleMenuClose();
  };

  const confirmDelete = async () => {
    if (!selectedContract) return;

    try {
      const response = await contractApi.deleteContract(selectedContract.uuid);
      if (response.success) {
        loadContracts();
        setDeleteDialog(false);
        setSelectedContract(null);
      }
    } catch (error) {
      console.error('Failed to delete contract:', error);
      alert('删除失败，请重试');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'default';
      case 'pending_signature': return 'warning';
      case 'confirmed': return 'info';
      case 'active': return 'success';
      case 'completed': return 'success';
      case 'cancelled': return 'error';
      case 'overdue': return 'error';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    const statusMap: { [key: string]: string } = {
      'draft': '草稿',
      'pending_signature': '待签署',
      'confirmed': '已确认',
      'active': '执行中',
      'completed': '已完成',
      'cancelled': '已取消',
      'overdue': '已逾期'
    };
    return statusMap[status] || status;
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleStatusFilterChange = (event: any) => {
    setStatusFilter(event.target.value);
    setPage(0);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          合同管理
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => navigate('/contracts/voice-recorder')}
        >
          新建合同
        </Button>
      </Box>

      {/* 搜索和过滤 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="搜索客户名称..."
                value={searchTerm}
                onChange={handleSearch}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>状态筛选</InputLabel>
                <Select
                  value={statusFilter}
                  label="状态筛选"
                  onChange={handleStatusFilterChange}
                >
                  <MenuItem value="">全部状态</MenuItem>
                  <MenuItem value="draft">草稿</MenuItem>
                  <MenuItem value="pending_signature">待签署</MenuItem>
                  <MenuItem value="confirmed">已确认</MenuItem>
                  <MenuItem value="active">执行中</MenuItem>
                  <MenuItem value="completed">已完成</MenuItem>
                  <MenuItem value="cancelled">已取消</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                startIcon={<FilterList />}
                onClick={loadContracts}
              >
                刷新
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 合同列表 */}
      <Card>
        <CardContent>
          {loading && <LinearProgress />}
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>合同编号</TableCell>
                  <TableCell>客户名称</TableCell>
                  <TableCell>合同金额</TableCell>
                  <TableCell>状态</TableCell>
                  <TableCell>创建时间</TableCell>
                  <TableCell>更新时间</TableCell>
                  <TableCell align="center">操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {contracts.map((contract) => (
                  <TableRow key={contract.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {contract.contractNumber}
                      </Typography>
                    </TableCell>
                    <TableCell>{contract.customerName}</TableCell>
                    <TableCell>
                      ¥{contract.amount?.toLocaleString() || 0}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(contract.status)}
                        color={getStatusColor(contract.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {new Date(contract.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      {contract.updatedAt ? 
                        new Date(contract.updatedAt).toLocaleDateString() : 
                        '-'
                      }
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        onClick={(e) => handleMenuClick(e, contract)}
                        size="small"
                      >
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={total}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage="每页行数:"
            labelDisplayedRows={({ from, to, count }) => 
              `${from}-${to} 共 ${count !== -1 ? count : `超过 ${to}`} 条`
            }
          />
        </CardContent>
      </Card>

      {/* 操作菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleViewDetail}>
          <Visibility sx={{ mr: 1 }} />
          查看详情
        </MenuItem>
        <MenuItem onClick={handleEdit}>
          <Edit sx={{ mr: 1 }} />
          编辑
        </MenuItem>
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <Delete sx={{ mr: 1 }} />
          删除
        </MenuItem>
      </Menu>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          <Typography>
            确定要删除合同 "{selectedContract?.contractNumber}" 吗？此操作不可撤销。
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>取消</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            删除
          </Button>
        </DialogActions>
      </Dialog>

      {/* 合同详情对话框 */}
      <Dialog 
        open={detailDialog} 
        onClose={() => setDetailDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>合同详情</DialogTitle>
        <DialogContent>
          {contractDetail && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={6}>
                <Typography variant="subtitle2">合同编号</Typography>
                <Typography>{contractDetail.contractNumber}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle2">客户名称</Typography>
                <Typography>{contractDetail.customerName}</Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle2">合同金额</Typography>
                <Typography>
                  {contractDetail.amount} {contractDetail.currency}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle2">状态</Typography>
                <Chip
                  label={getStatusLabel(contractDetail.status)}
                  color={getStatusColor(contractDetail.status) as any}
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2">创建时间</Typography>
                <Typography>
                  {new Date(contractDetail.createdAt).toLocaleString()}
                </Typography>
              </Grid>
              {contractDetail.contractData && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2">合同详细信息</Typography>
                  <Paper sx={{ p: 2, bgcolor: 'grey.50', mt: 1 }}>
                    <pre style={{ margin: 0, fontSize: '0.875rem', whiteSpace: 'pre-wrap' }}>
                      {JSON.stringify(contractDetail.contractData, null, 2)}
                    </pre>
                  </Paper>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialog(false)}>关闭</Button>
        </DialogActions>
      </Dialog>

      {/* 浮动操作按钮 */}
      <Tooltip title="语音录制合同">
        <Fab
          color="primary"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={() => navigate('/contracts/voice-recorder')}
        >
          <Mic />
        </Fab>
      </Tooltip>
    </Box>
  );
};

export default ContractList;
