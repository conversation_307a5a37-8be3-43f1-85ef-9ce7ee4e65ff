"""
多模态识别相关数据模型
"""

from sqlalchemy import Column, Integer, String, Text, JSON, Float, DateTime, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID
from enum import Enum
import uuid
from datetime import datetime

from app.models.base import BaseModel


class RecognitionType(str, Enum):
    """识别类型枚举"""
    SPEECH = "speech"
    VISION = "vision"
    DOCUMENT = "document"
    BIOMETRIC = "biometric"


class RecognitionStatus(str, Enum):
    """识别状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class RecognitionTask(BaseModel):
    """识别任务模型"""
    
    __tablename__ = "recognition_tasks"
    
    # 任务信息
    task_id = Column(String(100), unique=True, index=True, nullable=False)
    recognition_type = Column(SQLEnum(RecognitionType), nullable=False)
    status = Column(SQLEnum(RecognitionStatus), default=RecognitionStatus.PENDING, nullable=False)
    
    # 输入数据
    input_data = Column(JSON, nullable=False)  # 输入数据（JSON格式）
    config = Column(JSON, nullable=True)  # 识别配置
    
    # 处理信息
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    processing_time = Column(Float, nullable=True)  # 处理时间（秒）
    
    # 结果信息
    result = Column(JSON, nullable=True)  # 识别结果
    confidence_score = Column(Float, nullable=True)  # 置信度
    error_message = Column(Text, nullable=True)  # 错误信息
    
    # 元数据
    model_version = Column(String(50), nullable=True)  # 使用的模型版本
    processing_node = Column(String(100), nullable=True)  # 处理节点
    
    def start_processing(self):
        """开始处理"""
        self.status = RecognitionStatus.PROCESSING
        self.started_at = datetime.utcnow()
    
    def complete_processing(self, result: dict, confidence: float = None):
        """完成处理"""
        self.status = RecognitionStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        self.result = result
        self.confidence_score = confidence
        if self.started_at:
            self.processing_time = (self.completed_at - self.started_at).total_seconds()
    
    def fail_processing(self, error_message: str):
        """处理失败"""
        self.status = RecognitionStatus.FAILED
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        if self.started_at:
            self.processing_time = (self.completed_at - self.started_at).total_seconds()


class RecognitionResult(BaseModel):
    """识别结果模型"""
    
    __tablename__ = "recognition_results"
    
    # 关联信息
    task_id = Column(String(100), index=True, nullable=False)
    recognition_type = Column(SQLEnum(RecognitionType), nullable=False)
    
    # 结果数据
    result_data = Column(JSON, nullable=False)  # 详细结果数据
    confidence_score = Column(Float, nullable=True)  # 置信度
    processing_time = Column(Float, nullable=True)  # 处理时间
    
    # 质量指标
    accuracy_score = Column(Float, nullable=True)  # 准确率
    quality_score = Column(Float, nullable=True)  # 质量评分
    
    # 元数据
    model_info = Column(JSON, nullable=True)  # 模型信息
    processing_metadata = Column(JSON, nullable=True)  # 处理元数据


class SpeechRecognitionResult(BaseModel):
    """语音识别结果模型"""
    
    __tablename__ = "speech_recognition_results"
    
    # 关联信息
    task_id = Column(String(100), index=True, nullable=False)
    
    # 识别结果
    transcript = Column(Text, nullable=False)  # 转录文本
    language = Column(String(10), nullable=True)  # 识别语言
    confidence = Column(Float, nullable=True)  # 整体置信度
    
    # 音频信息
    duration = Column(Float, nullable=True)  # 音频时长（秒）
    sample_rate = Column(Integer, nullable=True)  # 采样率
    channels = Column(Integer, nullable=True)  # 声道数
    
    # 详细结果
    word_timestamps = Column(JSON, nullable=True)  # 词级时间戳
    sentence_segments = Column(JSON, nullable=True)  # 句子分段
    
    # 情感分析
    emotion_result = Column(JSON, nullable=True)  # 情感识别结果
    
    # 声纹识别
    speaker_info = Column(JSON, nullable=True)  # 说话人信息


class VisionRecognitionResult(BaseModel):
    """视觉识别结果模型"""
    
    __tablename__ = "vision_recognition_results"
    
    # 关联信息
    task_id = Column(String(100), index=True, nullable=False)
    
    # 图像信息
    image_width = Column(Integer, nullable=True)
    image_height = Column(Integer, nullable=True)
    image_format = Column(String(20), nullable=True)
    
    # 分类结果
    classification_result = Column(JSON, nullable=True)  # 图像分类结果
    
    # 检测结果
    detection_result = Column(JSON, nullable=True)  # 目标检测结果
    object_count = Column(Integer, nullable=True)  # 检测到的对象数量
    
    # 人脸识别
    face_detection_result = Column(JSON, nullable=True)  # 人脸检测结果
    face_count = Column(Integer, nullable=True)  # 检测到的人脸数量
    
    # 场景分析
    scene_analysis = Column(JSON, nullable=True)  # 场景分析结果
    
    # 质量评估
    image_quality_score = Column(Float, nullable=True)  # 图像质量评分


class DocumentRecognitionResult(BaseModel):
    """文档识别结果模型"""
    
    __tablename__ = "document_recognition_results"
    
    # 关联信息
    task_id = Column(String(100), index=True, nullable=False)
    
    # 文档信息
    document_type = Column(String(50), nullable=True)  # 文档类型
    page_count = Column(Integer, nullable=True)  # 页数
    
    # OCR结果
    extracted_text = Column(Text, nullable=True)  # 提取的文本
    text_confidence = Column(Float, nullable=True)  # 文本识别置信度
    
    # 结构化数据
    structured_data = Column(JSON, nullable=True)  # 结构化提取的数据
    
    # 表格识别
    table_data = Column(JSON, nullable=True)  # 表格数据
    table_count = Column(Integer, nullable=True)  # 表格数量
    
    # 特殊处理
    invoice_data = Column(JSON, nullable=True)  # 发票数据（如果是发票）
    contract_data = Column(JSON, nullable=True)  # 合同数据（如果是合同）
    
    # 版面分析
    layout_analysis = Column(JSON, nullable=True)  # 版面分析结果
    
    # 印章识别
    seal_detection = Column(JSON, nullable=True)  # 印章检测结果


class BiometricRecognitionResult(BaseModel):
    """生物特征识别结果模型"""
    
    __tablename__ = "biometric_recognition_results"
    
    # 关联信息
    task_id = Column(String(100), index=True, nullable=False)
    biometric_type = Column(String(20), nullable=False)  # 生物特征类型
    
    # 识别结果
    identity_result = Column(JSON, nullable=False)  # 身份识别结果
    confidence_score = Column(Float, nullable=True)  # 置信度
    
    # 质量评估
    quality_score = Column(Float, nullable=True)  # 生物特征质量评分
    quality_factors = Column(JSON, nullable=True)  # 质量因子详情
    
    # 匹配信息
    match_score = Column(Float, nullable=True)  # 匹配分数
    threshold_passed = Column(String(10), nullable=True)  # 是否通过阈值
    
    # 特征信息
    feature_vector = Column(JSON, nullable=True)  # 特征向量（加密存储）
    template_id = Column(String(100), nullable=True)  # 模板ID
    
    # 活体检测（针对人脸识别）
    liveness_result = Column(JSON, nullable=True)  # 活体检测结果


class RecognitionModel(BaseModel):
    """识别模型信息"""
    
    __tablename__ = "recognition_models"
    
    # 模型基本信息
    model_name = Column(String(100), nullable=False, index=True)
    model_type = Column(SQLEnum(RecognitionType), nullable=False)
    version = Column(String(50), nullable=False)
    
    # 模型描述
    description = Column(Text, nullable=True)
    capabilities = Column(JSON, nullable=True)  # 模型能力描述
    
    # 模型配置
    config = Column(JSON, nullable=True)  # 模型配置参数
    input_format = Column(JSON, nullable=True)  # 输入格式要求
    output_format = Column(JSON, nullable=True)  # 输出格式说明
    
    # 性能指标
    accuracy = Column(Float, nullable=True)  # 准确率
    precision = Column(Float, nullable=True)  # 精确率
    recall = Column(Float, nullable=True)  # 召回率
    f1_score = Column(Float, nullable=True)  # F1分数
    
    # 部署信息
    deployment_status = Column(String(20), default="inactive", nullable=False)
    model_path = Column(String(500), nullable=True)  # 模型文件路径
    api_endpoint = Column(String(200), nullable=True)  # API端点
    
    # 资源需求
    memory_requirement = Column(Integer, nullable=True)  # 内存需求（MB）
    gpu_requirement = Column(String(100), nullable=True)  # GPU需求
    cpu_cores = Column(Integer, nullable=True)  # CPU核心数需求
    
    # 使用统计
    usage_count = Column(Integer, default=0, nullable=False)  # 使用次数
    last_used_at = Column(DateTime, nullable=True)  # 最后使用时间
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.last_used_at = datetime.utcnow()


class RecognitionBatch(BaseModel):
    """批量识别任务"""
    
    __tablename__ = "recognition_batches"
    
    # 批次信息
    batch_id = Column(String(100), unique=True, index=True, nullable=False)
    batch_name = Column(String(200), nullable=True)
    recognition_type = Column(SQLEnum(RecognitionType), nullable=False)
    
    # 任务信息
    total_tasks = Column(Integer, default=0, nullable=False)
    completed_tasks = Column(Integer, default=0, nullable=False)
    failed_tasks = Column(Integer, default=0, nullable=False)
    
    # 状态信息
    status = Column(String(20), default="pending", nullable=False)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # 配置信息
    batch_config = Column(JSON, nullable=True)  # 批次配置
    
    # 结果统计
    average_confidence = Column(Float, nullable=True)  # 平均置信度
    average_processing_time = Column(Float, nullable=True)  # 平均处理时间
    
    def get_progress(self) -> float:
        """获取进度百分比"""
        if self.total_tasks == 0:
            return 0.0
        return (self.completed_tasks + self.failed_tasks) / self.total_tasks * 100
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        total_processed = self.completed_tasks + self.failed_tasks
        if total_processed == 0:
            return 0.0
        return self.completed_tasks / total_processed * 100
