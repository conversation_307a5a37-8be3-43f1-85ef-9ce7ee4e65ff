{"version": 3, "file": "measure-text-width.js", "sourceRoot": "", "sources": ["../src/measure-text-width.ts"], "names": [], "mappings": ";AAAA,OAAO,MAAM,MAAM,UAAU,CAAC;AAC9B,OAAO,OAAO,MAAM,WAAW,CAAC;AAChC,OAAO,QAAQ,MAAM,aAAa,CAAC;AAOnC,IAAI,GAA6B,CAAC;AAElC;;GAEG;AACH,eAAe,OAAO,CACpB,UAAC,IAAS,EAAE,IAAe;IAAf,qBAAA,EAAA,SAAe;IACjB,IAAA,QAAQ,GAAqD,IAAI,SAAzD,EAAE,UAAU,GAAyC,IAAI,WAA7C,EAAE,UAAU,GAA6B,IAAI,WAAjC,EAAE,SAAS,GAAkB,IAAI,UAAtB,EAAE,WAAW,GAAK,IAAI,YAAT,CAAU;IAC1E,IAAI,CAAC,GAAG,EAAE;QACR,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,CAA6B,CAAC;KACrF;IACD,GAAI,CAAC,IAAI,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAK,QAAQ,OAAI,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACxF,OAAO,GAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;AAC5D,CAAC,EACD,UAAC,IAAS,EAAE,IAAe;IAAf,qBAAA,EAAA,SAAe;IAAK,OAAA,gBAAC,IAAI,GAAK,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;AAAhC,CAAgC,CACjE,CAAC"}