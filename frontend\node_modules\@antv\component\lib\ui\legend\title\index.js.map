{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/ui/legend/title/index.ts"], "names": [], "mappings": ";;;AAmBA,0BAwCC;;AA3DD,sCAA0C;AAG1C,sCAAwH;AAKxH,IAAM,WAAW,GAAG,IAAA,iBAAU,EAC5B;IACE,IAAI,EAAE,MAAM;CACb,EACD,OAAO,CACR,CAAC;AAEF;;;GAGG;AACH,SAAgB,OAAO,CAAC,KAAY,EAAE,OAAsB;IACpD,IAAA,KAAqC,KAAK,CAAC,UAAuC,EAAhF,QAAQ,cAAA,EAAE,OAAO,aAAA,EAAE,KAAK,WAAA,EAAE,IAAI,UAAkD,CAAC;IACzF,IAAM,SAAS,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;IAClC,IAAM,WAAW,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IACtC,IAAM,GAAG,GAAG,IAAA,oBAAa,EAAC,QAAQ,CAAC,CAAC;IAC9B,IAAA,KAAA,eAAyD,IAAA,sBAAe,EAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,EAA3F,UAAU,QAAA,EAAE,YAAY,QAAA,EAAE,aAAa,QAAA,EAAE,WAAW,QAAuC,CAAC;IAC7F,IAAA,KAAA,eAAiD,IAAA,sBAAe,EAAC,KAAK,CAAC,IAAA,EAAtE,QAAQ,QAAA,EAAE,UAAU,QAAA,EAAE,WAAW,QAAA,EAAE,SAAS,QAA0B,CAAC;IACxE,IAAA,KAAA,eAAgC,CAAC,WAAW,GAAG,YAAY,EAAE,UAAU,GAAG,aAAa,CAAC,IAAA,EAAvF,YAAY,QAAA,EAAE,aAAa,QAA4D,CAAC;IACzF,IAAA,KAAA,eAA4B,CAAC,SAAS,GAAG,UAAU,EAAE,QAAQ,GAAG,WAAW,CAAC,IAAA,EAA3E,UAAU,QAAA,EAAE,WAAW,QAAoD,CAAC;IAEnF,kBAAkB;IAClB,yBAAyB;IACzB,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QACnB,OAAO,IAAI,WAAI,CACb,SAAS,CAAC,CAAC,EACX,SAAS,CAAC,CAAC,EACX,WAAW,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,GAAG,YAAY,GAAG,UAAU,EAC/D,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,CAC7D,CAAC;IACJ,CAAC;IACD,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QACnB,OAAO,IAAI,WAAI,CACb,SAAS,CAAC,CAAC,EACX,SAAS,CAAC,CAAC,EACX,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,GAAG,UAAU,EAAE,SAAS,CAAC,KAAK,CAAC,EACzD,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,aAAa,GAAG,WAAW,CACpE,CAAC;IACJ,CAAC;IACD,qDAAqD;IAE/C,IAAA,KAAA,eAAgC;QACpC,OAAO,CAAC,UAAU,CAAC,KAAK,IAAI,WAAW,CAAC,KAAK;QAC7C,OAAO,CAAC,UAAU,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM;KAChD,IAAA,EAHM,YAAY,QAAA,EAAE,aAAa,QAGjC,CAAC;IACF,OAAO,IAAI,WAAI,CACb,WAAW,CAAC,CAAC,EACb,WAAW,CAAC,CAAC,EACb,YAAY,GAAG,SAAS,CAAC,KAAK,GAAG,YAAY,GAAG,UAAU,EAC1D,aAAa,GAAG,SAAS,CAAC,MAAM,GAAG,aAAa,GAAG,WAAW,CAC/D,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CAAC,EAAa,EAAE,KAAU;IAC9C,IAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,EAAY;YAAZ,KAAA,qBAAY,EAAX,GAAG,QAAA,EAAE,KAAK,QAAA;QAC/D,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,QAAQ;YAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAChC,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAyB,CAAC,CAAC;IAE9B,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACxB,CAAC;AAED,SAAS,cAAc,CAAC,GAAoB;;IACpC,IAAA,KAA8B,GAAgC,EAA5D,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,QAAQ,cAAqC,CAAC;IAC/D,IAAA,KAAA,eAAW,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,IAAA,EAAnC,EAAE,QAAA,EAAE,EAAE,QAA6B,CAAC;IACvC,IAAA,KAAA,eAAkC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAA,EAA/D,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,SAAS,QAAA,EAAE,YAAY,QAAkC,CAAC;IACrE,IAAM,GAAG,GAAG,IAAA,oBAAa,EAAC,QAAQ,CAAC,CAAC;IAEpC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;QAAE,KAAA,eAAiB,CAAC,CAAC,EAAE,OAAO,CAAC,IAAA,EAA5B,CAAC,QAAA,EAAE,SAAS,QAAA,CAAiB;IACrD,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;QAAE,KAAA,eAAiB,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,IAAA,EAA/B,CAAC,QAAA,EAAE,SAAS,QAAA,CAAoB;IACxD,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;QAAE,KAAA,eAAoB,CAAC,CAAC,EAAE,KAAK,CAAC,IAAA,EAA7B,CAAC,QAAA,EAAE,YAAY,QAAA,CAAe;IACtD,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;QAAE,KAAA,eAAoB,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAA,EAAtC,CAAC,QAAA,EAAE,YAAY,QAAA,CAAwB;IAE/D,OAAO,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,CAAC;AAC3C,CAAC;AAED;IAA2B,iCAA0B;IAGnD,eAAY,OAAqB;QAC/B,OAAA,MAAK,YAAC,OAAO,EAAE;YACb,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,CAAC;YACR,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,MAAM;YAClB,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,YAAY;YACxB,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,UAAU;SACrB,CAAC,SAAC;IACL,CAAC;IAEM,iCAAiB,GAAxB;QACE,IAAM,SAAS,GAAG,IAAI,CAAC;QACjB,IAAA,KAMF,IAAI,CAAC,UAAuC,EALvC,cAAc,WAAA,EACb,eAAe,YAAA,EACvB,QAAQ,cAAA,EACR,OAAO,aAAA,EACP,KAAK,WACyC,CAAC;QACjD,IAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAgB,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7E,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,WAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,eAAe,CAAC,CAAC;QAC/D,IAAA,KAA6C,KAAK,CAAC,OAAO,EAAE,EAAnD,UAAU,WAAA,EAAU,WAAW,YAAoB,CAAC;QAC7D,IAAA,KAAA,eAAyD,IAAA,sBAAe,EAAC,OAAO,CAAC,IAAA,EAAhF,UAAU,QAAA,EAAE,YAAY,QAAA,EAAE,aAAa,QAAA,EAAE,WAAW,QAA4B,CAAC;QAEpF,IAAA,KAAA,eAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,eAAe,CAAC,IAAA,EAAhE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,KAAK,QAAA,EAAE,MAAM,QAA6C,CAAC;QACtE,IAAM,GAAG,GAAG,IAAA,oBAAa,EAAC,QAAQ,CAAC,CAAC;QAEpC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;YAAE,OAAO,IAAI,WAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAE5D,GAAG,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC;;YACf,IAAI,CAAC,KAAK,GAAG;gBACX,KAAA,eACE,CAAC,KAAK,CAAC;oBACL,CAAC,CAAC,CAAC,WAAW,GAAG,aAAa,EAAE,CAAC,eAAe,GAAG,WAAW,GAAG,aAAa,CAAC;oBAC/E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,IAAA,EAH1B,CAAC,QAAA,EAAE,MAAM,QAAA,CAGkB;YAC9B,IAAI,CAAC,KAAK,GAAG;gBAAE,KAAA,eAAU,CAAC,CAAC,cAAc,GAAG,UAAU,GAAG,WAAW,CAAC,IAAA,EAArD,KAAK,QAAA,CAAiD;YACtE,IAAI,CAAC,KAAK,GAAG;gBAAE,KAAA,eAAW,CAAC,CAAC,eAAe,GAAG,WAAW,GAAG,UAAU,CAAC,IAAA,EAAvD,MAAM,QAAA,CAAkD;YACxE,IAAI,CAAC,KAAK,GAAG;gBACX,KAAA,eACE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,YAAY,EAAE,CAAC,cAAc,GAAG,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAA,EAD1G,CAAC,QAAA,EAAE,KAAK,QAAA,CACmG;QAChH,CAAC,CAAC,CAAC;QAEG,IAAA,KAAA,eAAiD,IAAA,sBAAe,EAAC,KAAK,CAAC,IAAA,EAAtE,QAAQ,QAAA,EAAE,UAAU,QAAA,EAAE,WAAW,QAAA,EAAE,SAAS,QAA0B,CAAC;QACxE,IAAA,KAAA,eAA4B,CAAC,SAAS,GAAG,UAAU,EAAE,QAAQ,GAAG,WAAW,CAAC,IAAA,EAA3E,UAAU,QAAA,EAAE,WAAW,QAAoD,CAAC;QACnF,OAAO,IAAI,WAAI,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,QAAQ,EAAE,KAAK,GAAG,UAAU,EAAE,MAAM,GAAG,WAAW,CAAC,CAAC;IACzF,CAAC;IAEM,uBAAO,GAAd;QACE,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QAC5C,OAAO,IAAI,WAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9B,CAAC;IAEM,sBAAM,GAAb,UAAc,UAAqC,EAAE,SAAgB;QAArE,iBAaC;QAZS,IAAA,KAAK,GAA8C,UAAU,MAAxD,EAAE,MAAM,GAAsC,UAAU,OAAhD,EAAE,QAAQ,GAA4B,UAAU,SAAtC,EAAE,OAAO,GAAmB,UAAU,QAA7B,EAAK,SAAS,kBAAK,UAAU,EAA/D,0CAAkD,CAAF,CAAgB;QAEhE,IAAA,KAAA,eAAe,IAAA,iBAAU,EAAC,SAAS,CAAC,IAAA,EAAnC,UAAU,QAAyB,CAAC;QACrC,IAAA,KAAoC,cAAc,CAAC,UAAU,CAAC,EAA5D,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,SAAS,eAAA,EAAE,YAAY,kBAA+B,CAAC;QAErE,IAAA,aAAM,EAAC,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,IAAA,aAAM,EAAC,SAAS,CAAC,EAAE,UAAC,KAAK;YAChD,KAAI,CAAC,KAAK,GAAG,KAAK;iBACf,sBAAsB,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC;iBAChD,MAAM,CAAC,UAAU,CAAC;iBAClB,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,CAAC;iBACtD,IAAI,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;IACL,CAAC;IACH,YAAC;AAAD,CAAC,AA1ED,CAA2B,gBAAS,GA0EnC;AA1EY,sBAAK"}