{"name": "@ant-design/graphs", "version": "2.1.1", "description": "A React graph library based on Graphin", "keywords": ["antv", "g6", "graph", "graph analysis", "graph editor", "graph visualization", "relational data", "react"], "bugs": {"url": "https://github.com/ant-design/ant-design-charts/issues"}, "repository": {"type": "git", "url": "git+https://github.com/ant-design/ant-design-charts.git"}, "license": "MIT", "main": "lib/index.js", "unpkg": "dist/graphs.min.js", "module": "es/index.js", "types": "es/index.d.ts", "files": ["lib", "dist", "es", "src"], "dependencies": {"@ant-design/charts-util": "0.0.1-alpha.7", "@antv/g6": "^5.0.44", "@antv/g6-extension-react": "^0.2.0", "@antv/graphin": "^3.0.4", "lodash": "^4.17.21", "styled-components": "^6.1.15"}, "devDependencies": {"@antv/algorithm": "^0.1.26", "@types/jest": "^26.0.24", "@types/lodash": "^4.17.16", "antd": "^5.24.3", "react-router-dom": "^6.30.0", "tsconfig-paths-webpack-plugin": "^4.2.0", "vite": "^5.4.14"}, "peerDependencies": {"react": ">=16.8.4", "react-dom": ">=16.8.4"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "scripts": {"build": "run-p build:*", "build:esm": "rimraf ./es && tsc --module ESNext --outDir es -p tsconfig.prod.json", "build:lib": "rimraf ./lib && tsc --module commonjs --outDir lib -p tsconfig.prod.json", "build:umd": "rimraf ./dist && webpack --config webpack.config.js --mode production", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "ci": "run-s lint build test", "clean": "rimraf lib es dist", "dev": "vite", "lint": "eslint --cache --ext .js,.jsx,.ts,.tsx  --fix --format=pretty ./src && npm run lint:prettier", "lint:prettier": "npm run prettier && git diff && prettier --version && prettier --check \"src/**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "prettier": "prettier --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "profile": "webpack --config webpack.config.js --mode production --profile --json > stats.json", "start": "npm run build:esm --w", "test": "jest"}}