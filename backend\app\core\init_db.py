"""
数据库初始化脚本
"""

import logging
from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine
from app.models.base import BaseModel
from app.models.user import User, UserRole
from app.models.agent import Agent
from app.models.workflow import Workflow, WorkflowStep
from app.models.business import Customer, Contract
from app.models.device import Device
from app.models.monitoring import MonitoringMetric
from app.models.recognition import RecognitionModel
from app.core.security import get_password_hash
import uuid
from datetime import datetime, date
import json

logger = logging.getLogger(__name__)


def init_database():
    """初始化数据库"""
    try:
        # 创建所有表
        BaseModel.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
        # 初始化基础数据
        db = SessionLocal()
        try:
            init_user_roles(db)
            init_admin_user(db)
            init_recognition_models(db)
            init_sample_data(db)
            logger.info("Database initialization completed successfully")
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


def init_user_roles(db: Session):
    """初始化用户角色"""
    roles_data = [
        {
            "name": "admin",
            "description": "系统管理员",
            "permissions": {
                "users": ["create", "read", "update", "delete"],
                "agents": ["create", "read", "update", "delete"],
                "workflows": ["create", "read", "update", "delete"],
                "contracts": ["create", "read", "update", "delete"],
                "recognition": ["create", "read", "update", "delete"],
                "monitoring": ["read"],
                "system": ["manage"]
            }
        },
        {
            "name": "manager",
            "description": "业务管理员",
            "permissions": {
                "users": ["read"],
                "agents": ["create", "read", "update"],
                "workflows": ["create", "read", "update"],
                "contracts": ["create", "read", "update", "delete"],
                "recognition": ["create", "read"],
                "monitoring": ["read"]
            }
        },
        {
            "name": "operator",
            "description": "操作员",
            "permissions": {
                "agents": ["read", "update"],
                "workflows": ["read", "update"],
                "contracts": ["create", "read", "update"],
                "recognition": ["create", "read"],
                "monitoring": ["read"]
            }
        },
        {
            "name": "viewer",
            "description": "只读用户",
            "permissions": {
                "agents": ["read"],
                "workflows": ["read"],
                "contracts": ["read"],
                "recognition": ["read"],
                "monitoring": ["read"]
            }
        }
    ]
    
    for role_data in roles_data:
        existing_role = db.query(UserRole).filter(UserRole.name == role_data["name"]).first()
        if not existing_role:
            role = UserRole(
                name=role_data["name"],
                description=role_data["description"],
                permissions=role_data["permissions"],
                created_at=datetime.utcnow()
            )
            db.add(role)
    
    db.commit()
    logger.info("User roles initialized")


def init_admin_user(db: Session):
    """初始化管理员用户"""
    admin_email = "<EMAIL>"
    existing_admin = db.query(User).filter(User.email == admin_email).first()
    
    if not existing_admin:
        admin_user = User(
            uuid=str(uuid.uuid4()),
            username="admin",
            email=admin_email,
            hashed_password=get_password_hash("admin123"),
            full_name="系统管理员",
            is_active=True,
            is_superuser=True,
            department="技术部",
            position="系统管理员",
            created_at=datetime.utcnow()
        )
        db.add(admin_user)
        db.commit()
        logger.info("Admin user created")
    else:
        logger.info("Admin user already exists")


def init_recognition_models(db: Session):
    """初始化识别模型"""
    models_data = [
        {
            "model_name": "whisper-base",
            "model_type": "speech",
            "version": "1.0.0",
            "description": "基础语音识别模型",
            "capabilities": {
                "languages": ["zh-CN", "en-US", "ja-JP"],
                "max_duration": 300,
                "sample_rates": [16000, 44100],
                "formats": ["wav", "mp3", "m4a"]
            },
            "config": {
                "model_size": "base",
                "language": "auto",
                "temperature": 0.0
            },
            "accuracy": 0.92,
            "precision": 0.89,
            "recall": 0.94,
            "deployment_status": "active"
        },
        {
            "model_name": "yolo-v8",
            "model_type": "vision",
            "version": "8.0.0",
            "description": "YOLO v8目标检测模型",
            "capabilities": {
                "classes": 80,
                "max_resolution": [1920, 1080],
                "formats": ["jpg", "png", "bmp"],
                "detection_types": ["object", "person", "vehicle"]
            },
            "config": {
                "confidence_threshold": 0.5,
                "iou_threshold": 0.45,
                "max_detections": 100
            },
            "accuracy": 0.89,
            "precision": 0.87,
            "recall": 0.91,
            "deployment_status": "active"
        },
        {
            "model_name": "paddleocr",
            "model_type": "document",
            "version": "2.7.0",
            "description": "PaddleOCR文档识别模型",
            "capabilities": {
                "languages": ["ch", "en", "ja"],
                "formats": ["pdf", "jpg", "png"],
                "features": ["text_detection", "text_recognition", "table_extraction"]
            },
            "config": {
                "use_angle_cls": True,
                "use_gpu": False,
                "det_limit_side_len": 960
            },
            "accuracy": 0.94,
            "precision": 0.92,
            "recall": 0.96,
            "deployment_status": "active"
        },
        {
            "model_name": "facenet",
            "model_type": "biometric",
            "version": "1.0.0",
            "description": "FaceNet人脸识别模型",
            "capabilities": {
                "biometric_types": ["face"],
                "operations": ["identify", "verify", "enroll"],
                "formats": ["jpg", "png"],
                "min_face_size": [112, 112]
            },
            "config": {
                "embedding_size": 512,
                "margin": 0.5,
                "scale": 64.0
            },
            "accuracy": 0.96,
            "precision": 0.94,
            "recall": 0.98,
            "deployment_status": "active"
        }
    ]
    
    for model_data in models_data:
        existing_model = db.query(RecognitionModel).filter(
            RecognitionModel.model_name == model_data["model_name"]
        ).first()
        
        if not existing_model:
            model = RecognitionModel(
                model_name=model_data["model_name"],
                model_type=model_data["model_type"],
                version=model_data["version"],
                description=model_data["description"],
                capabilities=model_data["capabilities"],
                config=model_data["config"],
                accuracy=model_data["accuracy"],
                precision=model_data["precision"],
                recall=model_data["recall"],
                deployment_status=model_data["deployment_status"],
                created_at=datetime.utcnow()
            )
            db.add(model)
    
    db.commit()
    logger.info("Recognition models initialized")


def init_sample_data(db: Session):
    """初始化示例数据"""
    # 创建示例客户
    sample_customers = [
        {
            "name": "北京科技有限公司",
            "contact_person": "张经理",
            "phone": "010-12345678",
            "email": "<EMAIL>",
            "industry": "软件开发",
            "company_size": "中型企业",
            "credit_rating": "A",
            "risk_level": "low"
        },
        {
            "name": "上海贸易集团",
            "contact_person": "李总监",
            "phone": "021-87654321",
            "email": "<EMAIL>",
            "industry": "贸易",
            "company_size": "大型企业",
            "credit_rating": "AA",
            "risk_level": "low"
        },
        {
            "name": "深圳创新工作室",
            "contact_person": "王设计师",
            "phone": "0755-11223344",
            "email": "<EMAIL>",
            "industry": "设计服务",
            "company_size": "小型企业",
            "credit_rating": "B",
            "risk_level": "medium"
        }
    ]
    
    for customer_data in sample_customers:
        existing_customer = db.query(Customer).filter(
            Customer.name == customer_data["name"]
        ).first()
        
        if not existing_customer:
            customer = Customer(
                uuid=str(uuid.uuid4()),
                name=customer_data["name"],
                contact_person=customer_data["contact_person"],
                phone=customer_data["phone"],
                email=customer_data["email"],
                industry=customer_data["industry"],
                company_size=customer_data["company_size"],
                credit_rating=customer_data["credit_rating"],
                risk_level=customer_data["risk_level"],
                created_at=datetime.utcnow()
            )
            db.add(customer)
    
    db.commit()
    
    # 创建示例智能体
    sample_agents = [
        {
            "name": "语音识别助手",
            "description": "专门处理语音识别任务的智能体",
            "agent_type": "recognition",
            "config": {
                "recognition_type": "speech",
                "model": "whisper-base",
                "language": "zh-CN"
            },
            "status": "active"
        },
        {
            "name": "合同管理助手",
            "description": "智能合同处理和管理助手",
            "agent_type": "business",
            "config": {
                "features": ["voice_input", "risk_assessment", "contract_generation"],
                "templates": ["service_contract", "sales_contract"]
            },
            "status": "active"
        },
        {
            "name": "文档分析助手",
            "description": "文档识别和分析处理助手",
            "agent_type": "recognition",
            "config": {
                "recognition_type": "document",
                "model": "paddleocr",
                "features": ["ocr", "table_extraction"]
            },
            "status": "active"
        }
    ]
    
    for agent_data in sample_agents:
        existing_agent = db.query(Agent).filter(
            Agent.name == agent_data["name"]
        ).first()
        
        if not existing_agent:
            agent = Agent(
                uuid=str(uuid.uuid4()),
                name=agent_data["name"],
                description=agent_data["description"],
                agent_type=agent_data["agent_type"],
                config=agent_data["config"],
                status=agent_data["status"],
                version="1.0.0",
                created_at=datetime.utcnow()
            )
            db.add(agent)
    
    db.commit()
    
    # 创建示例工作流
    sample_workflows = [
        {
            "name": "语音合同处理流程",
            "description": "从语音录入到合同生成的完整流程",
            "workflow_type": "business",
            "definition": {
                "steps": [
                    {
                        "id": "input_1",
                        "type": "input",
                        "name": "语音输入",
                        "config": {"input_type": "voice"}
                    },
                    {
                        "id": "recognition_1",
                        "type": "recognition",
                        "name": "语音识别",
                        "config": {"recognition_type": "speech", "model": "whisper-base"}
                    },
                    {
                        "id": "processing_1",
                        "type": "processing",
                        "name": "信息提取",
                        "config": {"processing_type": "extract_contract_info"}
                    },
                    {
                        "id": "action_1",
                        "type": "action",
                        "name": "生成合同",
                        "config": {"action_type": "generate_contract"}
                    },
                    {
                        "id": "output_1",
                        "type": "output",
                        "name": "输出结果",
                        "config": {"format": "json"}
                    }
                ],
                "connections": [
                    {"source": "input_1", "target": "recognition_1"},
                    {"source": "recognition_1", "target": "processing_1"},
                    {"source": "processing_1", "target": "action_1"},
                    {"source": "action_1", "target": "output_1"}
                ]
            },
            "status": "active"
        }
    ]
    
    for workflow_data in sample_workflows:
        existing_workflow = db.query(Workflow).filter(
            Workflow.name == workflow_data["name"]
        ).first()
        
        if not existing_workflow:
            workflow = Workflow(
                uuid=str(uuid.uuid4()),
                name=workflow_data["name"],
                description=workflow_data["description"],
                workflow_type=workflow_data["workflow_type"],
                definition=json.dumps(workflow_data["definition"]),
                status=workflow_data["status"],
                version="1.0.0",
                created_at=datetime.utcnow()
            )
            db.add(workflow)
    
    db.commit()
    logger.info("Sample data initialized")


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    init_database()
