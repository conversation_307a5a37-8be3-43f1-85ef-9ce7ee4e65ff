"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2018_asyncgenerator = void 0;
const base_config_1 = require("./base-config");
const es2018_asynciterable_1 = require("./es2018.asynciterable");
exports.es2018_asyncgenerator = Object.assign(Object.assign({}, es2018_asynciterable_1.es2018_asynciterable), { AsyncGenerator: base_config_1.TYPE, AsyncGeneratorFunction: base_config_1.TYPE, AsyncGeneratorFunctionConstructor: base_config_1.TYPE });
//# sourceMappingURL=es2018.asyncgenerator.js.map