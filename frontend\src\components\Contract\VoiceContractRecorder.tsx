import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  IconButton,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  FormControlLabel,
  Switch,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  <PERSON><PERSON>,
  Mic<PERSON><PERSON>,
  PlayArrow,
  Stop,
  Save,
  Send,
  CheckCircle,
  Warning,
  Info,
  ExpandMore,
  Person,
  Business,
  AttachMoney,
  Description,
  Schedule,
  Security
} from '@mui/icons-material';
import { contractApi } from '../../services/api';

interface VoiceContractData {
  transcript: string;
  contractInfo: {
    customerName: string;
    contactPerson: string;
    phone: string;
    email: string;
    contractType: string;
    serviceDescription: string;
    amount: number;
    currency: string;
    duration: string;
    startDate: string;
    paymentTerms: string;
    specialTerms: string;
    contractNumber: string;
    isComplete: boolean;
    missingFields: string[];
  };
  riskAssessment: {
    riskLevel: string;
    riskScore: number;
    factors: any;
    recommendations: string[];
    isNewCustomer: boolean;
  };
  contractDraft: string;
  status: string;
  nextSteps: string[];
}

const VoiceContractRecorder: React.FC = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [contractData, setContractData] = useState<VoiceContractData | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [editDialog, setEditDialog] = useState(false);
  const [editedInfo, setEditedInfo] = useState<any>({});
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      });
      
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        setAudioBlob(audioBlob);
        
        // 创建音频URL用于播放
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
        
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start(1000); // 每秒收集一次数据
      setIsRecording(true);
      setRecordingTime(0);
      
      // 开始计时
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

    } catch (error) {
      console.error('Error starting recording:', error);
      alert('无法访问麦克风，请检查权限设置');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  const playRecording = () => {
    if (audioUrl && audioRef.current) {
      audioRef.current.src = audioUrl;
      audioRef.current.play();
    }
  };

  const processVoiceContract = async () => {
    if (!audioBlob) {
      alert('请先录制语音');
      return;
    }

    setIsProcessing(true);
    try {
      // 将音频转换为base64
      const reader = new FileReader();
      reader.onload = async () => {
        const base64Audio = (reader.result as string).split(',')[1];
        
        const voiceData = {
          audio_base64: base64Audio,
          format: 'webm',
          duration: recordingTime,
          timestamp: new Date().toISOString()
        };

        const response = await contractApi.processVoiceContract(voiceData);
        
        if (response.success) {
          setContractData(response.data);
          setActiveStep(1);
        } else {
          alert('语音处理失败，请重试');
        }
      };
      reader.readAsDataURL(audioBlob);
      
    } catch (error) {
      console.error('Voice contract processing failed:', error);
      alert('语音合同处理失败，请重试');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleEditInfo = () => {
    if (contractData) {
      setEditedInfo({ ...contractData.contractInfo });
      setEditDialog(true);
    }
  };

  const saveEditedInfo = () => {
    if (contractData) {
      setContractData({
        ...contractData,
        contractInfo: { ...editedInfo }
      });
    }
    setEditDialog(false);
  };

  const generateFinalContract = async () => {
    if (!contractData) return;

    setIsProcessing(true);
    try {
      const approvalData = {
        approved: true,
        approver: 'current_user',
        approvalTime: new Date().toISOString(),
        modifications: editedInfo
      };

      const response = await contractApi.generateFinalContract(
        contractData.contractInfo.contractNumber,
        approvalData
      );

      if (response.success) {
        setActiveStep(2);
        alert('最终合同生成成功！');
      }
    } catch (error) {
      console.error('Final contract generation failed:', error);
      alert('最终合同生成失败');
    } finally {
      setIsProcessing(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'success';
      case 'medium': return 'warning';
      case 'high': return 'error';
      default: return 'default';
    }
  };

  const steps = [
    '语音录制',
    '信息确认',
    '合同生成'
  ];

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        智能语音合同录入
      </Typography>

      <Stepper activeStep={activeStep} orientation="vertical">
        <Step>
          <StepLabel>语音录制与识别</StepLabel>
          <StepContent>
            <Card>
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ textAlign: 'center' }}>
                      <IconButton
                        size="large"
                        color={isRecording ? "secondary" : "primary"}
                        onClick={isRecording ? stopRecording : startRecording}
                        disabled={isProcessing}
                        sx={{ 
                          width: 100, 
                          height: 100,
                          border: 3,
                          borderColor: isRecording ? 'secondary.main' : 'primary.main',
                          mb: 2
                        }}
                      >
                        {isRecording ? 
                          <MicOff sx={{ fontSize: 50 }} /> : 
                          <Mic sx={{ fontSize: 50 }} />
                        }
                      </IconButton>
                      
                      <Typography variant="h6">
                        {isRecording ? '录制中...' : '点击开始录制'}
                      </Typography>
                      
                      {isRecording && (
                        <Typography variant="h4" color="secondary" sx={{ mt: 1 }}>
                          {formatTime(recordingTime)}
                        </Typography>
                      )}
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" gutterBottom>
                      录制指南
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon><Person /></ListItemIcon>
                        <ListItemText primary="客户基本信息" secondary="姓名、联系方式、公司名称" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><Business /></ListItemIcon>
                        <ListItemText primary="合同类型" secondary="服务合同、销售合同等" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><AttachMoney /></ListItemIcon>
                        <ListItemText primary="金额信息" secondary="合同总金额、付款方式" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><Schedule /></ListItemIcon>
                        <ListItemText primary="时间安排" secondary="合同期限、开始时间" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><Description /></ListItemIcon>
                        <ListItemText primary="服务内容" secondary="详细的服务或产品描述" />
                      </ListItem>
                    </List>
                  </Grid>
                </Grid>

                {audioBlob && (
                  <Box sx={{ mt: 3 }}>
                    <Divider sx={{ mb: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      录制完成
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                      <Button
                        variant="outlined"
                        startIcon={<PlayArrow />}
                        onClick={playRecording}
                      >
                        播放录音
                      </Button>
                      <Button
                        variant="contained"
                        startIcon={<Send />}
                        onClick={processVoiceContract}
                        disabled={isProcessing}
                      >
                        {isProcessing ? '处理中...' : '开始识别'}
                      </Button>
                      <Typography variant="body2" color="textSecondary">
                        录制时长: {formatTime(recordingTime)}
                      </Typography>
                    </Box>
                    
                    {isProcessing && (
                      <Box sx={{ mt: 2 }}>
                        <LinearProgress />
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          正在进行语音识别和信息提取...
                        </Typography>
                      </Box>
                    )}
                  </Box>
                )}
              </CardContent>
            </Card>
          </StepContent>
        </Step>

        <Step>
          <StepLabel>信息确认与风险评估</StepLabel>
          <StepContent>
            {contractData && (
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="h6">
                          提取的合同信息
                        </Typography>
                        <Button
                          variant="outlined"
                          onClick={handleEditInfo}
                          startIcon={<Description />}
                        >
                          编辑信息
                        </Button>
                      </Box>

                      {contractData.contractInfo.missingFields.length > 0 && (
                        <Alert severity="warning" sx={{ mb: 2 }}>
                          缺少以下信息: {contractData.contractInfo.missingFields.join(', ')}
                        </Alert>
                      )}

                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="subtitle2">客户名称</Typography>
                          <Typography>{contractData.contractInfo.customerName}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="subtitle2">联系人</Typography>
                          <Typography>{contractData.contractInfo.contactPerson}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="subtitle2">电话</Typography>
                          <Typography>{contractData.contractInfo.phone}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="subtitle2">邮箱</Typography>
                          <Typography>{contractData.contractInfo.email}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="subtitle2">合同类型</Typography>
                          <Typography>{contractData.contractInfo.contractType}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="subtitle2">合同金额</Typography>
                          <Typography>
                            {contractData.contractInfo.amount} {contractData.contractInfo.currency}
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography variant="subtitle2">服务描述</Typography>
                          <Typography>{contractData.contractInfo.serviceDescription}</Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        风险评估
                      </Typography>
                      
                      <Box sx={{ mb: 2 }}>
                        <Chip
                          label={`风险等级: ${contractData.riskAssessment.riskLevel.toUpperCase()}`}
                          color={getRiskLevelColor(contractData.riskAssessment.riskLevel) as any}
                          icon={<Security />}
                        />
                      </Box>

                      <Typography variant="body2" gutterBottom>
                        风险评分: {(contractData.riskAssessment.riskScore * 100).toFixed(0)}%
                      </Typography>

                      <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
                        建议措施:
                      </Typography>
                      <List dense>
                        {contractData.riskAssessment.recommendations.map((rec, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <Info fontSize="small" />
                            </ListItemIcon>
                            <ListItemText primary={rec} />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMore />}>
                      <Typography variant="h6">合同草稿预览</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                        <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit' }}>
                          {contractData.contractDraft}
                        </pre>
                      </Paper>
                    </AccordionDetails>
                  </Accordion>
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                      variant="contained"
                      onClick={generateFinalContract}
                      disabled={!contractData.contractInfo.isComplete || isProcessing}
                      startIcon={<CheckCircle />}
                    >
                      生成最终合同
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={() => setActiveStep(0)}
                    >
                      重新录制
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            )}
          </StepContent>
        </Step>

        <Step>
          <StepLabel>合同生成完成</StepLabel>
          <StepContent>
            <Alert severity="success" sx={{ mb: 2 }}>
              合同已成功生成并发送给客户确认！
            </Alert>
            
            <Typography variant="h6" gutterBottom>
              后续步骤:
            </Typography>
            <List>
              {contractData?.nextSteps.map((step, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <CheckCircle color="primary" />
                  </ListItemIcon>
                  <ListItemText primary={step} />
                </ListItem>
              ))}
            </List>

            <Box sx={{ mt: 3 }}>
              <Button
                variant="contained"
                onClick={() => {
                  setActiveStep(0);
                  setContractData(null);
                  setAudioBlob(null);
                  if (audioUrl) {
                    URL.revokeObjectURL(audioUrl);
                    setAudioUrl(null);
                  }
                }}
              >
                录制新合同
              </Button>
            </Box>
          </StepContent>
        </Step>
      </Stepper>

      {/* 编辑信息对话框 */}
      <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>编辑合同信息</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="客户名称"
                value={editedInfo.customerName || ''}
                onChange={(e) => setEditedInfo(prev => ({ ...prev, customerName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="联系人"
                value={editedInfo.contactPerson || ''}
                onChange={(e) => setEditedInfo(prev => ({ ...prev, contactPerson: e.target.value }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="电话"
                value={editedInfo.phone || ''}
                onChange={(e) => setEditedInfo(prev => ({ ...prev, phone: e.target.value }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="邮箱"
                value={editedInfo.email || ''}
                onChange={(e) => setEditedInfo(prev => ({ ...prev, email: e.target.value }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="合同金额"
                type="number"
                value={editedInfo.amount || ''}
                onChange={(e) => setEditedInfo(prev => ({ ...prev, amount: parseFloat(e.target.value) }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="合同期限"
                value={editedInfo.duration || ''}
                onChange={(e) => setEditedInfo(prev => ({ ...prev, duration: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="服务描述"
                value={editedInfo.serviceDescription || ''}
                onChange={(e) => setEditedInfo(prev => ({ ...prev, serviceDescription: e.target.value }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)}>取消</Button>
          <Button onClick={saveEditedInfo} variant="contained">保存</Button>
        </DialogActions>
      </Dialog>

      {/* 隐藏的音频元素 */}
      <audio ref={audioRef} style={{ display: 'none' }} />
    </Box>
  );
};

export default VoiceContractRecorder;
