import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';
import { API_BASE_URL, API_TIMEOUT } from '@/utils/constants';
import { getToken, removeToken, showError } from '../utils/helpers';

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加认证token
    const token = getToken();
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加请求ID用于追踪
    if (config.headers) {
      config.headers['X-Request-ID'] = Date.now().toString();
    }

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 统一处理响应数据
    const { data } = response;
    
    // 如果后端返回的是标准格式 { success, data, message }
    if (data && typeof data === 'object' && 'success' in data) {
      if (data.success) {
        return { ...response, data: data.data };
      } else {
        // 业务错误
        const errorMessage = data.message || '操作失败';
        message.error(errorMessage);
        return Promise.reject(new Error(errorMessage));
      }
    }
    
    return response;
  },
  (error) => {
    console.error('Response interceptor error:', error);
    
    // 处理网络错误
    if (!error.response) {
      message.error('网络连接失败，请检查网络设置');
      return Promise.reject(error);
    }

    const { status, data } = error.response;
    
    switch (status) {
      case 401:
        // 未授权，清除token并跳转到登录页
        message.error('登录已过期，请重新登录');
        removeToken();
        window.location.href = '/login';
        break;
        
      case 403:
        message.error('没有权限执行此操作');
        break;
        
      case 404:
        message.error('请求的资源不存在');
        break;
        
      case 422:
        // 表单验证错误
        if (data && data.errors) {
          const errorMessages = Object.values(data.errors).flat();
          message.error(errorMessages.join(', '));
        } else {
          message.error('请求参数错误');
        }
        break;
        
      case 429:
        message.error('请求过于频繁，请稍后再试');
        break;
        
      case 500:
        message.error('服务器内部错误');
        break;
        
      case 502:
      case 503:
      case 504:
        message.error('服务暂时不可用，请稍后再试');
        break;
        
      default:
        const errorMessage = data?.message || `请求失败 (${status})`;
        message.error(errorMessage);
    }
    
    return Promise.reject(error);
  }
);

// 通用请求方法
export const request = {
  get: <T = any>(url: string, config?: InternalAxiosRequestConfig): Promise<AxiosResponse<T>> =>
    api.get(url, config),

  post: <T = any>(url: string, data?: any, config?: InternalAxiosRequestConfig): Promise<AxiosResponse<T>> =>
    api.post(url, data, config),

  put: <T = any>(url: string, data?: any, config?: InternalAxiosRequestConfig): Promise<AxiosResponse<T>> =>
    api.put(url, data, config),

  patch: <T = any>(url: string, data?: any, config?: InternalAxiosRequestConfig): Promise<AxiosResponse<T>> =>
    api.patch(url, data, config),

  delete: <T = any>(url: string, config?: InternalAxiosRequestConfig): Promise<AxiosResponse<T>> =>
    api.delete(url, config),
};

// 文件上传方法
export const uploadFile = (
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<AxiosResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  
  return api.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    },
  });
};

// 下载文件方法
export const downloadFile = (
  url: string,
  filename?: string,
  config?: InternalAxiosRequestConfig
): Promise<void> => {
  return api.get(url, {
    ...config,
    responseType: 'blob',
  }).then((response) => {
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  });
};

// 批量请求方法
export const batchRequest = async <T = any>(
  requests: Array<() => Promise<AxiosResponse<T>>>
): Promise<T[]> => {
  try {
    const responses = await Promise.allSettled(requests.map(req => req()));
    const results: T[] = [];
    const errors: string[] = [];
    
    responses.forEach((response, index) => {
      if (response.status === 'fulfilled') {
        results.push(response.value.data);
      } else {
        errors.push(`请求 ${index + 1} 失败: ${response.reason.message}`);
      }
    });
    
    if (errors.length > 0) {
      console.warn('批量请求部分失败:', errors);
    }
    
    return results;
  } catch (error) {
    console.error('批量请求失败:', error);
    throw error;
  }
};

// 取消请求的控制器
export const createCancelToken = () => {
  const controller = new AbortController();
  return {
    token: controller.signal,
    cancel: (reason?: string) => controller.abort(reason),
  };
};

// 重试请求方法
export const retryRequest = async <T = any>(
  requestFn: () => Promise<AxiosResponse<T>>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<AxiosResponse<T>> => {
  let lastError: any;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error;
      
      if (i === maxRetries) {
        break;
      }
      
      // 指数退避延迟
      const waitTime = delay * Math.pow(2, i);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  throw lastError;
};

// 识别API
export const recognitionApi = {
  // 通用识别接口
  recognize: (data: any) => request.post('/recognition/recognize', data),

  // 语音识别
  speechRecognize: (data: any) => request.post('/recognition/speech/recognize', data),

  // 视觉识别
  visionAnalyze: (data: any) => request.post('/recognition/vision/analyze', data),

  // 文档识别
  documentExtract: (data: any) => request.post('/recognition/document/extract', data),

  // 生物特征识别
  biometricIdentify: (data: any) => request.post('/recognition/biometric/identify', data),

  // 文件上传识别
  uploadAndRecognize: (formData: FormData, recognitionType: string, config?: string) => {
    const params = new URLSearchParams();
    params.append('recognition_type', recognitionType);
    if (config) {
      params.append('config', config);
    }

    return api.post(`/recognition/upload?${params.toString()}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000, // 增加超时时间用于文件上传
    });
  },

  // 批量识别
  batchRecognize: (data: any) => request.post('/recognition/batch', data),

  // 获取任务详情
  getTask: (taskId: string) => request.get(`/recognition/tasks/${taskId}`),

  // 获取识别历史
  getHistory: (params?: any) => request.get('/recognition/history', { params }),

  // 获取统计信息
  getStatistics: (params?: any) => request.get('/recognition/statistics', { params }),

  // 健康检查
  healthCheck: () => request.get('/recognition/health'),
};

// 合同API
export const contractApi = {
  // 处理语音合同
  processVoiceContract: (data: any) => request.post('/contracts/voice-contract', data),

  // 上传语音文件
  uploadVoiceContract: (formData: FormData, config?: string) => {
    const params = new URLSearchParams();
    if (config) {
      params.append('config', config);
    }

    return api.post(`/contracts/upload-voice?${params.toString()}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000,
    });
  },

  // 生成最终合同
  generateFinalContract: (contractId: string, approvalData: any) =>
    request.post(`/contracts/generate-final/${contractId}`, approvalData),

  // 确认合同
  confirmContract: (contractId: string, confirmationData: any) =>
    request.post(`/contracts/confirm/${contractId}`, confirmationData),

  // 获取合同列表
  getContractList: (params?: any) => request.get('/contracts/list', { params }),

  // 获取合同详情
  getContract: (contractId: string) => request.get(`/contracts/${contractId}`),

  // 更新合同
  updateContract: (contractId: string, data: any) =>
    request.put(`/contracts/${contractId}`, data),

  // 删除合同
  deleteContract: (contractId: string) => request.delete(`/contracts/${contractId}`),

  // 客户风险评估
  assessCustomerRisk: (customerName: string) =>
    request.get(`/contracts/customers/risk-assessment/${encodeURIComponent(customerName)}`),

  // 获取合同模板
  getContractTemplates: (params?: any) =>
    request.get('/contracts/templates/list', { params }),

  // 获取合同统计
  getContractStatistics: (params?: any) =>
    request.get('/contracts/statistics/dashboard', { params }),
};

export default api;
