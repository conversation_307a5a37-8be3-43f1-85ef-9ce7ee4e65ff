{"version": 3, "file": "category.js", "sourceRoot": "", "sources": ["../../../src/ui/legend/category.ts"], "names": [], "mappings": ";;;;AAAA,mCAAuC;AAEvC,mCAAgF;AAEhF,iCAAyC;AACzC,0CAAiD;AACjD,uCAAmE;AAKnE;IAA8B,oCAA6B;IACzD,kBAAY,OAAwB;QAClC,OAAA,MAAK,YAAC,OAAO,EAAE,mCAAwB,CAAC,SAAC;IAC3C,CAAC;IAUO,8BAAW,GAAnB,UAAoB,SAAoB,EAAE,KAAa,EAAE,MAAc;QAC/D,IAAA,KAA2B,IAAI,CAAC,UAAU,EAAxC,SAAS,eAAA,EAAE,SAAS,eAAoB,CAAC;QACjD,IAAM,KAAK,GAAG,IAAA,oBAAa,EAAkB,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACjE,IAAA,KAAA,eAA2B,IAAA,iBAAU,EAAC,KAAK,CAAC,IAAA,EAA3C,UAAU,QAAA,EAAE,UAAU,QAAqB,CAAC;QAEnD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,sBAAsB,CAAQ,sBAAW,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAE1G,IAAM,eAAe,uCAAK,KAAK,OAAA,EAAE,MAAM,QAAA,IAAK,UAAU,KAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,GAAE,CAAC;QAC3F,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU;aACzB,sBAAsB,CAAC,sBAAW,CAAC,KAAK,EAAE,cAAM,OAAA,IAAI,aAAK,CAAC,EAAE,KAAK,EAAE,eAAkC,EAAE,CAAC,EAAxD,CAAwD,CAAC;aACzG,MAAM,CAAC,eAAe,CAAqB,CAAC;IACjD,CAAC;IAEO,8BAAW,GAAnB,UAAoB,SAAoB,EAAE,IAAa;QAC7C,IAAA,CAAC,GAAuB,IAAI,EAA3B,EAAE,CAAC,GAAoB,IAAI,EAAxB,EAAE,KAAK,GAAa,IAAI,MAAjB,EAAE,MAAM,GAAK,IAAI,OAAT,CAAU;QACrC,IAAM,KAAK,GAAG,IAAA,oBAAa,EAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACtD,IAAA,KAAA,eAAiC,IAAA,iBAAU,EAAC,KAAK,CAAC,IAAA,EAAjD,gBAAgB,QAAA,EAAE,UAAU,QAAqB,CAAC;QACzD,gDAAgD;QAChD,yBAAyB;QACzB,IAAM,SAAS,GAAG,sCAAK,gBAAgB,KAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAwB,CAAC;QAC3F,IAAI,CAAC,UAAU,GAAG,SAAS;aACxB,sBAAsB,CAAQ,sBAAW,CAAC,UAAU,EAAE,GAAG,CAAC;aAC1D,MAAM,uCAAM,UAAU,KAAE,SAAS,EAAE,oBAAa,CAAC,eAAK,CAAC,MAAG,IAAG,CAAC;QACjE,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,UAAU;aACZ,SAAS,CAAC,sBAAW,CAAC,KAAK,CAAC,KAAK,CAAC;aAClC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;aACf,IAAI,CACH,UAAC,KAAK;YACJ,OAAA,KAAK;iBACF,MAAM,CAAC,cAAM,OAAA,IAAI,qBAAa,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,EAAvC,CAAuC,CAAC;iBACrD,IAAI,CAAC,WAAW,EAAE,sBAAW,CAAC,KAAK,CAAC,IAAI,CAAC;iBACzC,IAAI,CAAC;gBACJ,IAAI,CAAC,KAAK,GAAG,IAAA,aAAM,EAAC,IAAI,CAAC,CAAC;YAC5B,CAAC,CAAC;QALJ,CAKI,EACN,UAAC,MAAM,IAAK,OAAA,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,EAAxB,CAAwB,EACpC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,EAAE,EAAb,CAAa,CACxB,CAAC;IACN,CAAC;IAEO,+BAAY,GAApB;QACU,IAAA,SAAS,GAAK,IAAI,CAAC,UAAU,UAApB,CAAqB;QACtC,IAAI,SAAS,EAAE,CAAC;YACR,IAAA,KAAW,IAAI,CAAC,KAAK,CAAC,IAAI,EAAS,CAAC,iBAAiB,EAAE,EAArD,CAAC,OAAA,EAAE,CAAC,OAAiD,CAAC;YAC9D,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,oBAAa,CAAC,eAAK,CAAC,MAAG,CAAC;QACnE,CAAC;IACH,CAAC;IAED,sBAAY,oCAAc;aAA1B;YACQ,IAAA,KAA+B,IAAI,CAAC,UAAU,EAA5C,SAAS,eAAA,EAAE,KAAK,WAAA,EAAE,MAAM,YAAoB,CAAC;YACrD,IAAI,CAAC,SAAS;gBAAE,OAAO,IAAI,WAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAM,EAAE,MAAO,CAAC,CAAC;YACvD,OAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,EAAY,CAAC,iBAAiB,EAAE,CAAC;QAC1D,CAAC;;;OAAA;IAEM,0BAAO,GAAd;;QACE,IAAM,KAAK,GAAU,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,EAAE,CAAC;QACxC,IAAM,KAAK,GAAG,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK;YAAE,OAAO,gBAAK,CAAC,OAAO,WAAE,CAAC;QAC7C,OAAO,IAAA,eAAO,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,yBAAM,GAAN,UAAO,UAAwC,EAAE,SAAgB;QACzD,IAAA,KAAkC,IAAI,CAAC,UAAU,EAA/C,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,SAAK,EAAL,CAAC,mBAAG,CAAC,KAAA,EAAE,SAAK,EAAL,CAAC,mBAAG,CAAC,KAAoB,CAAC;QACxD,IAAM,GAAG,GAAG,IAAA,aAAM,EAAC,SAAS,CAAC,CAAC;QAC9B,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,oBAAa,CAAC,eAAK,CAAC,MAAG,CAAC;QAEpD,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAM,EAAE,MAAO,CAAC,CAAC;QAEvC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAE3C,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IACH,eAAC;AAAD,CAAC,AArFD,CAA8B,gBAAS,GAqFtC;AArFY,4BAAQ"}