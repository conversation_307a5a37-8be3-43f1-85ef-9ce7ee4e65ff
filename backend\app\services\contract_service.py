"""
智能合同管理服务
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json
import uuid
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import smtplib

from sqlalchemy.orm import Session
from app.core.config import settings
from app.models.business import Contract, Customer, ContractStatus
from app.services.recognition_service import SpeechRecognitionService

logger = logging.getLogger(__name__)


class ContractService:
    """智能合同管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.speech_service = SpeechRecognitionService()
        self.risk_service = CustomerRiskService(db)
        self.email_service = EmailService()
        self.template_service = ContractTemplateService()
    
    async def process_voice_contract(self, voice_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理语音合同录入"""
        try:
            # 1. 语音识别转文字
            recognition_request = {
                "recognition_type": "speech",
                "input_data": voice_data,
                "config": {
                    "language": "zh-CN",
                    "enable_emotion": True,
                    "domain": "business"
                }
            }
            
            speech_result = await self.speech_service.recognize(recognition_request)
            transcript = speech_result.get("transcript", "")
            
            # 2. 提取合同信息
            contract_info = await self._extract_contract_info(transcript)
            
            # 3. 验证和补全信息
            validated_info = await self._validate_contract_info(contract_info)
            
            # 4. 客户风险评估
            risk_assessment = await self.risk_service.assess_customer_risk(
                validated_info.get("customer_name", "")
            )
            
            # 5. 生成合同草稿
            contract_draft = await self.template_service.generate_contract(
                validated_info, risk_assessment
            )
            
            return {
                "transcript": transcript,
                "contract_info": validated_info,
                "risk_assessment": risk_assessment,
                "contract_draft": contract_draft,
                "status": "draft_generated",
                "next_steps": self._get_next_steps(risk_assessment)
            }
            
        except Exception as e:
            logger.error(f"Voice contract processing failed: {e}")
            return {
                "status": "failed",
                "error_message": str(e),
                "transcript": voice_data.get("transcript", "")
            }
    
    async def _extract_contract_info(self, transcript: str) -> Dict[str, Any]:
        """从语音转录文本中提取合同信息"""
        # 这里应该使用NLP模型来提取结构化信息
        # 目前使用模拟数据
        await asyncio.sleep(0.1)
        
        # 模拟信息提取结果
        return {
            "customer_name": "示例客户公司",
            "contact_person": "张三",
            "phone": "13800138000",
            "email": "<EMAIL>",
            "contract_type": "服务合同",
            "service_description": "软件开发服务",
            "amount": 100000.0,
            "currency": "CNY",
            "duration": "6个月",
            "start_date": "2024-09-01",
            "payment_terms": "分期付款",
            "special_terms": "包含技术支持",
            "confidence": 0.85
        }
    
    async def _validate_contract_info(self, contract_info: Dict[str, Any]) -> Dict[str, Any]:
        """验证和补全合同信息"""
        validated_info = contract_info.copy()
        
        # 验证必填字段
        required_fields = ["customer_name", "amount", "contract_type"]
        missing_fields = []
        
        for field in required_fields:
            if not validated_info.get(field):
                missing_fields.append(field)
        
        # 格式化金额
        if validated_info.get("amount"):
            try:
                validated_info["amount"] = float(validated_info["amount"])
            except (ValueError, TypeError):
                validated_info["amount"] = 0.0
        
        # 生成合同编号
        validated_info["contract_number"] = f"CT{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:6].upper()}"
        
        # 设置默认值
        validated_info.setdefault("currency", "CNY")
        validated_info.setdefault("status", "draft")
        validated_info["missing_fields"] = missing_fields
        validated_info["is_complete"] = len(missing_fields) == 0
        
        return validated_info
    
    def _get_next_steps(self, risk_assessment: Dict[str, Any]) -> List[str]:
        """根据风险评估结果确定下一步操作"""
        risk_level = risk_assessment.get("risk_level", "medium")
        next_steps = []
        
        if risk_level == "high":
            next_steps.extend([
                "需要额外的信用审查",
                "建议增加担保条款",
                "需要法务部门审核"
            ])
        elif risk_level == "medium":
            next_steps.extend([
                "标准审核流程",
                "确认客户资质"
            ])
        else:
            next_steps.append("可以直接生成正式合同")
        
        next_steps.extend([
            "发送合同确认邮件",
            "等待客户确认",
            "安排签约时间"
        ])
        
        return next_steps
    
    async def generate_final_contract(self, contract_id: str, approval_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终合同"""
        try:
            # 获取合同草稿
            contract = self.db.query(Contract).filter(Contract.uuid == contract_id).first()
            if not contract:
                raise ValueError(f"Contract not found: {contract_id}")
            
            # 应用审批意见
            contract_data = json.loads(contract.contract_data)
            contract_data.update(approval_data)
            
            # 生成正式合同文档
            final_contract = await self.template_service.generate_final_contract(contract_data)
            
            # 更新合同状态
            contract.status = ContractStatus.PENDING_SIGNATURE
            contract.contract_data = json.dumps(contract_data)
            contract.updated_at = datetime.utcnow()
            self.db.commit()
            
            # 发送合同确认邮件
            email_result = await self.email_service.send_contract_confirmation(
                contract_data, final_contract
            )
            
            return {
                "contract_id": contract_id,
                "final_contract": final_contract,
                "email_sent": email_result.get("success", False),
                "status": "pending_signature",
                "confirmation_link": f"{settings.BASE_URL}/contracts/{contract_id}/confirm"
            }
            
        except Exception as e:
            logger.error(f"Final contract generation failed: {e}")
            raise
    
    async def confirm_contract(self, contract_id: str, confirmation_data: Dict[str, Any]) -> Dict[str, Any]:
        """客户确认合同"""
        try:
            contract = self.db.query(Contract).filter(Contract.uuid == contract_id).first()
            if not contract:
                raise ValueError(f"Contract not found: {contract_id}")
            
            # 更新合同状态
            contract.status = ContractStatus.CONFIRMED
            contract.confirmed_at = datetime.utcnow()
            contract.confirmation_data = json.dumps(confirmation_data)
            self.db.commit()
            
            # 发送确认通知
            await self.email_service.send_contract_confirmed_notification(contract)
            
            return {
                "contract_id": contract_id,
                "status": "confirmed",
                "confirmed_at": contract.confirmed_at.isoformat(),
                "next_step": "arrange_signing"
            }
            
        except Exception as e:
            logger.error(f"Contract confirmation failed: {e}")
            raise


class CustomerRiskService:
    """客户风险评估服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def assess_customer_risk(self, customer_name: str) -> Dict[str, Any]:
        """评估客户风险"""
        try:
            # 查询客户历史记录
            customer = self.db.query(Customer).filter(Customer.name == customer_name).first()
            
            if not customer:
                # 新客户，进行基础风险评估
                return await self._assess_new_customer(customer_name)
            else:
                # 现有客户，基于历史记录评估
                return await self._assess_existing_customer(customer)
                
        except Exception as e:
            logger.error(f"Customer risk assessment failed: {e}")
            return {
                "risk_level": "unknown",
                "risk_score": 0.5,
                "assessment_error": str(e)
            }
    
    async def _assess_new_customer(self, customer_name: str) -> Dict[str, Any]:
        """评估新客户风险"""
        # 模拟新客户风险评估
        await asyncio.sleep(0.1)
        
        return {
            "risk_level": "medium",
            "risk_score": 0.6,
            "factors": {
                "credit_history": "unknown",
                "business_registration": "pending_verification",
                "industry_risk": "medium",
                "payment_history": "no_history"
            },
            "recommendations": [
                "验证企业资质",
                "要求预付款",
                "设置较短的付款周期"
            ],
            "is_new_customer": True
        }
    
    async def _assess_existing_customer(self, customer: Customer) -> Dict[str, Any]:
        """评估现有客户风险"""
        # 模拟现有客户风险评估
        await asyncio.sleep(0.1)
        
        # 查询历史合同
        contracts = self.db.query(Contract).filter(Contract.customer_id == customer.id).all()
        
        # 计算风险指标
        total_contracts = len(contracts)
        completed_contracts = len([c for c in contracts if c.status == ContractStatus.COMPLETED])
        overdue_contracts = len([c for c in contracts if c.status == ContractStatus.OVERDUE])
        
        success_rate = completed_contracts / total_contracts if total_contracts > 0 else 0
        overdue_rate = overdue_contracts / total_contracts if total_contracts > 0 else 0
        
        # 计算风险等级
        if success_rate > 0.9 and overdue_rate < 0.1:
            risk_level = "low"
            risk_score = 0.2
        elif success_rate > 0.7 and overdue_rate < 0.3:
            risk_level = "medium"
            risk_score = 0.5
        else:
            risk_level = "high"
            risk_score = 0.8
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "factors": {
                "total_contracts": total_contracts,
                "success_rate": success_rate,
                "overdue_rate": overdue_rate,
                "last_contract_date": contracts[-1].created_at.isoformat() if contracts else None,
                "average_contract_value": sum(c.amount for c in contracts) / total_contracts if contracts else 0
            },
            "recommendations": self._get_risk_recommendations(risk_level),
            "is_new_customer": False
        }
    
    def _get_risk_recommendations(self, risk_level: str) -> List[str]:
        """根据风险等级获取建议"""
        if risk_level == "low":
            return ["可以提供标准付款条件", "考虑给予优惠条件"]
        elif risk_level == "medium":
            return ["标准风险控制措施", "定期跟进付款情况"]
        else:
            return ["要求预付款或担保", "缩短付款周期", "加强合同条款"]


class EmailService:
    """邮件发送服务"""
    
    def __init__(self):
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT
        self.smtp_user = settings.SMTP_USER
        self.smtp_password = settings.SMTP_PASSWORD
    
    async def send_contract_confirmation(self, contract_data: Dict[str, Any], contract_document: str) -> Dict[str, Any]:
        """发送合同确认邮件"""
        try:
            # 构建邮件内容
            subject = f"合同确认 - {contract_data.get('contract_number', 'N/A')}"
            
            html_content = f"""
            <html>
            <body>
                <h2>合同确认通知</h2>
                <p>尊敬的 {contract_data.get('contact_person', '客户')}：</p>
                <p>您的合同已生成，请查看并确认以下信息：</p>
                
                <table border="1" style="border-collapse: collapse;">
                    <tr><td>合同编号</td><td>{contract_data.get('contract_number', 'N/A')}</td></tr>
                    <tr><td>客户名称</td><td>{contract_data.get('customer_name', 'N/A')}</td></tr>
                    <tr><td>合同金额</td><td>{contract_data.get('amount', 0)} {contract_data.get('currency', 'CNY')}</td></tr>
                    <tr><td>合同期限</td><td>{contract_data.get('duration', 'N/A')}</td></tr>
                </table>
                
                <p>请点击以下链接确认合同：</p>
                <a href="{settings.BASE_URL}/contracts/{contract_data.get('contract_id', '')}/confirm">确认合同</a>
                
                <p>如有疑问，请联系我们。</p>
                <p>谢谢！</p>
            </body>
            </html>
            """
            
            # 发送邮件（模拟）
            await asyncio.sleep(0.1)
            logger.info(f"Contract confirmation email sent to {contract_data.get('email', 'unknown')}")
            
            return {
                "success": True,
                "message": "邮件发送成功",
                "recipient": contract_data.get("email", "unknown")
            }
            
        except Exception as e:
            logger.error(f"Failed to send contract confirmation email: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def send_contract_confirmed_notification(self, contract: Contract) -> Dict[str, Any]:
        """发送合同确认通知"""
        try:
            # 模拟发送内部通知邮件
            await asyncio.sleep(0.05)
            logger.info(f"Contract confirmed notification sent for contract {contract.uuid}")
            
            return {
                "success": True,
                "message": "确认通知发送成功"
            }
            
        except Exception as e:
            logger.error(f"Failed to send contract confirmed notification: {e}")
            return {
                "success": False,
                "error": str(e)
            }


class ContractTemplateService:
    """合同模板服务"""
    
    def __init__(self):
        self.templates = self._load_templates()
    
    def _load_templates(self) -> Dict[str, str]:
        """加载合同模板"""
        return {
            "service_contract": """
            服务合同模板
            
            甲方：{customer_name}
            乙方：本公司
            
            合同编号：{contract_number}
            签订日期：{contract_date}
            
            一、服务内容
            {service_description}
            
            二、合同金额
            总金额：{amount} {currency}
            
            三、付款方式
            {payment_terms}
            
            四、服务期限
            {duration}
            
            五、特殊条款
            {special_terms}
            """,
            
            "sales_contract": """
            销售合同模板
            
            买方：{customer_name}
            卖方：本公司
            
            合同编号：{contract_number}
            签订日期：{contract_date}
            
            一、产品信息
            {product_description}
            
            二、合同金额
            总金额：{amount} {currency}
            
            三、交付条款
            {delivery_terms}
            
            四、付款条款
            {payment_terms}
            """
        }
    
    async def generate_contract(self, contract_info: Dict[str, Any], risk_assessment: Dict[str, Any]) -> str:
        """生成合同草稿"""
        try:
            contract_type = contract_info.get("contract_type", "service_contract")
            template_key = "service_contract" if "服务" in contract_type else "sales_contract"
            
            template = self.templates.get(template_key, self.templates["service_contract"])
            
            # 填充模板
            contract_draft = template.format(
                customer_name=contract_info.get("customer_name", ""),
                contract_number=contract_info.get("contract_number", ""),
                contract_date=datetime.now().strftime("%Y年%m月%d日"),
                service_description=contract_info.get("service_description", ""),
                amount=contract_info.get("amount", 0),
                currency=contract_info.get("currency", "CNY"),
                payment_terms=contract_info.get("payment_terms", ""),
                duration=contract_info.get("duration", ""),
                special_terms=contract_info.get("special_terms", ""),
                product_description=contract_info.get("product_description", ""),
                delivery_terms=contract_info.get("delivery_terms", "")
            )
            
            # 根据风险评估添加额外条款
            if risk_assessment.get("risk_level") == "high":
                contract_draft += "\n\n六、风险控制条款\n"
                contract_draft += "- 要求预付款50%\n"
                contract_draft += "- 提供履约保证金\n"
            
            return contract_draft
            
        except Exception as e:
            logger.error(f"Contract generation failed: {e}")
            return f"合同生成失败：{str(e)}"
    
    async def generate_final_contract(self, contract_data: Dict[str, Any]) -> str:
        """生成最终合同"""
        # 在草稿基础上生成正式合同
        draft = await self.generate_contract(contract_data, contract_data.get("risk_assessment", {}))
        
        # 添加法律条款和签名区域
        final_contract = draft + """
        
        七、争议解决
        本合同履行过程中发生的争议，双方应友好协商解决；协商不成的，提交有管辖权的人民法院解决。
        
        八、其他条款
        本合同一式两份，双方各执一份，具有同等法律效力。
        本合同自双方签字盖章之日起生效。
        
        甲方（盖章）：________________    乙方（盖章）：________________
        
        代表签字：____________________    代表签字：____________________
        
        日期：________________________    日期：________________________
        """
        
        return final_contract
