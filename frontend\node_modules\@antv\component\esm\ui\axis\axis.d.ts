import type { GenericAnimation } from '../../animation';
import { Component } from '../../core';
import { Group } from '../../shapes';
import type { AxisOptions, AxisStyleProps, RequiredAxisStyleProps } from './types';
export type { ArcAxisOptions, ArcAxisStyleProps, AxisOptions, AxisStyleProps, LinearAxisOptions, LinearAxisStyleProps, } from './types';
export declare class Axis extends Component<AxisStyleProps> {
    constructor(options: AxisOptions);
    render(attributes: RequiredAxisStyleProps, container: Group, specificAnimation?: GenericAnimation): import("@antv/g-lite").IAnimation[];
}
