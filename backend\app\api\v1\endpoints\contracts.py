"""
智能合同管理API端点
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.models.business import Contract, Customer, ContractStatus
from app.services.contract_service import ContractService
import logging
import json
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/voice-contract")
async def process_voice_contract(
    voice_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """处理语音合同录入"""
    try:
        service = ContractService(db)
        result = await service.process_voice_contract(voice_data)
        
        return {
            "success": True,
            "data": result,
            "message": "语音合同处理成功"
        }
        
    except Exception as e:
        logger.error(f"Voice contract processing failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"语音合同处理失败: {str(e)}"
        )


@router.post("/upload-voice")
async def upload_voice_contract(
    file: UploadFile = File(...),
    config: Optional[str] = Query(None, description="配置参数（JSON字符串）"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """上传语音文件进行合同录入"""
    try:
        # 验证文件类型
        if not file.filename.lower().endswith(('.wav', '.mp3', '.m4a', '.flac')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的音频文件格式"
            )
        
        # 读取文件内容
        file_content = await file.read()
        
        # 构建语音数据
        import base64
        voice_data = {
            "audio_base64": base64.b64encode(file_content).decode('utf-8'),
            "format": file.filename.split('.')[-1],
            "filename": file.filename,
            "size": len(file_content)
        }
        
        # 解析配置
        config_dict = json.loads(config) if config else {}
        voice_data.update(config_dict)
        
        service = ContractService(db)
        result = await service.process_voice_contract(voice_data)
        
        return {
            "success": True,
            "data": result,
            "message": "语音文件上传并处理成功"
        }
        
    except Exception as e:
        logger.error(f"Voice file upload failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"语音文件处理失败: {str(e)}"
        )


@router.post("/generate-final/{contract_id}")
async def generate_final_contract(
    contract_id: str,
    approval_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """生成最终合同"""
    try:
        service = ContractService(db)
        result = await service.generate_final_contract(contract_id, approval_data)
        
        return {
            "success": True,
            "data": result,
            "message": "最终合同生成成功"
        }
        
    except Exception as e:
        logger.error(f"Final contract generation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"最终合同生成失败: {str(e)}"
        )


@router.post("/confirm/{contract_id}")
async def confirm_contract(
    contract_id: str,
    confirmation_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """客户确认合同"""
    try:
        service = ContractService(db)
        result = await service.confirm_contract(contract_id, confirmation_data)
        
        return {
            "success": True,
            "data": result,
            "message": "合同确认成功"
        }
        
    except Exception as e:
        logger.error(f"Contract confirmation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"合同确认失败: {str(e)}"
        )


@router.get("/list")
async def list_contracts(
    status: Optional[ContractStatus] = Query(None, description="合同状态过滤"),
    customer_name: Optional[str] = Query(None, description="客户名称过滤"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="页大小"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取合同列表"""
    try:
        query = db.query(Contract)
        
        # 应用过滤条件
        if status:
            query = query.filter(Contract.status == status)
        
        if customer_name:
            query = query.join(Customer).filter(Customer.name.ilike(f"%{customer_name}%"))
        
        # 分页
        total = query.count()
        contracts = query.offset((page - 1) * size).limit(size).all()
        
        return {
            "success": True,
            "data": {
                "contracts": [
                    {
                        "id": contract.id,
                        "uuid": contract.uuid,
                        "contract_number": contract.contract_number,
                        "customer_name": contract.customer.name if contract.customer else None,
                        "amount": contract.amount,
                        "status": contract.status,
                        "created_at": contract.created_at.isoformat(),
                        "updated_at": contract.updated_at.isoformat() if contract.updated_at else None
                    }
                    for contract in contracts
                ],
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size
            }
        }
        
    except Exception as e:
        logger.error(f"List contracts failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取合同列表失败: {str(e)}"
        )


@router.get("/{contract_id}")
async def get_contract(
    contract_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取合同详情"""
    try:
        contract = db.query(Contract).filter(Contract.uuid == contract_id).first()
        if not contract:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="合同不存在"
            )
        
        # 解析合同数据
        contract_data = json.loads(contract.contract_data) if contract.contract_data else {}
        
        return {
            "success": True,
            "data": {
                "id": contract.id,
                "uuid": contract.uuid,
                "contract_number": contract.contract_number,
                "customer_id": contract.customer_id,
                "customer_name": contract.customer.name if contract.customer else None,
                "amount": contract.amount,
                "currency": contract.currency,
                "status": contract.status,
                "contract_data": contract_data,
                "created_at": contract.created_at.isoformat(),
                "updated_at": contract.updated_at.isoformat() if contract.updated_at else None,
                "confirmed_at": contract.confirmed_at.isoformat() if contract.confirmed_at else None
            }
        }
        
    except Exception as e:
        logger.error(f"Get contract failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取合同详情失败: {str(e)}"
        )


@router.put("/{contract_id}")
async def update_contract(
    contract_id: str,
    update_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新合同信息"""
    try:
        contract = db.query(Contract).filter(Contract.uuid == contract_id).first()
        if not contract:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="合同不存在"
            )
        
        # 更新合同字段
        for field, value in update_data.items():
            if hasattr(contract, field) and field not in ['id', 'uuid', 'created_at']:
                setattr(contract, field, value)
        
        contract.updated_at = datetime.utcnow()
        db.commit()
        
        return {
            "success": True,
            "message": "合同更新成功"
        }
        
    except Exception as e:
        logger.error(f"Update contract failed: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新合同失败: {str(e)}"
        )


@router.delete("/{contract_id}")
async def delete_contract(
    contract_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除合同"""
    try:
        contract = db.query(Contract).filter(Contract.uuid == contract_id).first()
        if not contract:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="合同不存在"
            )
        
        # 检查合同状态，只允许删除草稿状态的合同
        if contract.status not in [ContractStatus.DRAFT, ContractStatus.CANCELLED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能删除草稿或已取消状态的合同"
            )
        
        db.delete(contract)
        db.commit()
        
        return {
            "success": True,
            "message": "合同删除成功"
        }
        
    except Exception as e:
        logger.error(f"Delete contract failed: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除合同失败: {str(e)}"
        )


@router.get("/customers/risk-assessment/{customer_name}")
async def assess_customer_risk(
    customer_name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """评估客户风险"""
    try:
        service = ContractService(db)
        risk_assessment = await service.risk_service.assess_customer_risk(customer_name)
        
        return {
            "success": True,
            "data": risk_assessment,
            "message": "客户风险评估完成"
        }
        
    except Exception as e:
        logger.error(f"Customer risk assessment failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"客户风险评估失败: {str(e)}"
        )


@router.get("/templates/list")
async def list_contract_templates(
    template_type: Optional[str] = Query(None, description="模板类型"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取合同模板列表"""
    try:
        service = ContractService(db)
        templates = service.template_service.templates
        
        # 过滤模板类型
        if template_type:
            templates = {k: v for k, v in templates.items() if template_type in k}
        
        return {
            "success": True,
            "data": {
                "templates": [
                    {
                        "key": key,
                        "name": key.replace("_", " ").title(),
                        "content": template[:200] + "..." if len(template) > 200 else template
                    }
                    for key, template in templates.items()
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"List contract templates failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取合同模板失败: {str(e)}"
        )


@router.get("/statistics/dashboard")
async def get_contract_statistics(
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取合同统计信息"""
    try:
        # 基础统计查询
        total_contracts = db.query(Contract).count()
        draft_contracts = db.query(Contract).filter(Contract.status == ContractStatus.DRAFT).count()
        active_contracts = db.query(Contract).filter(Contract.status == ContractStatus.ACTIVE).count()
        completed_contracts = db.query(Contract).filter(Contract.status == ContractStatus.COMPLETED).count()
        
        # 按状态分组统计
        status_stats = {}
        for status in ContractStatus:
            count = db.query(Contract).filter(Contract.status == status).count()
            status_stats[status.value] = count
        
        # 计算总金额
        from sqlalchemy import func
        total_amount = db.query(func.sum(Contract.amount)).scalar() or 0
        
        return {
            "success": True,
            "data": {
                "total_contracts": total_contracts,
                "draft_contracts": draft_contracts,
                "active_contracts": active_contracts,
                "completed_contracts": completed_contracts,
                "total_amount": total_amount,
                "status_distribution": status_stats,
                "completion_rate": (completed_contracts / total_contracts * 100) if total_contracts > 0 else 0
            }
        }
        
    except Exception as e:
        logger.error(f"Get contract statistics failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取合同统计失败: {str(e)}"
        )
