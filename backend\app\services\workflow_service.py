"""
工作流服务
"""

import uuid
import asyncio
import json
from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime
from enum import Enum

from app.models.workflow import Workflow, WorkflowStep, WorkflowExecution, WorkflowStatus, ExecutionStatus
from app.schemas.workflow import WorkflowCreate, WorkflowUpdate, WorkflowExecutionCreate
from app.services.recognition_service import RecognitionService
from app.services.contract_service import ContractService
import logging

logger = logging.getLogger(__name__)


class StepType(str, Enum):
    """步骤类型枚举"""
    INPUT = "input"
    PROCESSING = "processing"
    RECOGNITION = "recognition"
    DECISION = "decision"
    ACTION = "action"
    OUTPUT = "output"
    PARALLEL = "parallel"
    LOOP = "loop"


class WorkflowService:
    """工作流服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.recognition_service = RecognitionService(db)
        self.contract_service = ContractService(db)
        self.step_processors = self._init_step_processors()

    def _init_step_processors(self) -> Dict[str, Any]:
        """初始化步骤处理器"""
        return {
            StepType.INPUT: self._process_input_step,
            StepType.PROCESSING: self._process_processing_step,
            StepType.RECOGNITION: self._process_recognition_step,
            StepType.DECISION: self._process_decision_step,
            StepType.ACTION: self._process_action_step,
            StepType.OUTPUT: self._process_output_step,
            StepType.PARALLEL: self._process_parallel_step,
            StepType.LOOP: self._process_loop_step
        }
    
    async def get_workflows(
        self,
        skip: int = 0,
        limit: int = 10,
        status: Optional[str] = None,
        category: Optional[str] = None,
        search: Optional[str] = None,
        user_id: Optional[int] = None
    ) -> Tuple[List[Workflow], int]:
        """获取工作流列表"""
        query = self.db.query(Workflow).filter(Workflow.is_deleted == False)
        
        # 过滤条件
        if status:
            query = query.filter(Workflow.status == status)
        
        if category:
            query = query.filter(Workflow.category == category)
        
        if search:
            query = query.filter(
                or_(
                    Workflow.name.contains(search),
                    Workflow.description.contains(search)
                )
            )
        
        if user_id:
            query = query.filter(Workflow.creator_id == user_id)
        
        # 获取总数
        total = query.count()
        
        # 分页和排序
        workflows = query.order_by(Workflow.created_at.desc()).offset(skip).limit(limit).all()
        
        return workflows, total
    
    async def create_workflow(self, workflow_data: WorkflowCreate, creator_id: int) -> Workflow:
        """创建工作流"""
        try:
            workflow = Workflow(
                name=workflow_data.name,
                description=workflow_data.description,
                category=workflow_data.category,
                definition=workflow_data.definition,
                variables=workflow_data.variables,
                triggers=workflow_data.triggers,
                timeout_seconds=workflow_data.timeout_seconds or 3600,
                max_retries=workflow_data.max_retries or 3,
                creator_id=creator_id,
                status=WorkflowStatus.DRAFT
            )
            
            self.db.add(workflow)
            self.db.commit()
            self.db.refresh(workflow)
            
            # 创建工作流步骤
            if workflow_data.steps:
                await self._create_workflow_steps(workflow.id, workflow_data.steps)
            
            logger.info(f"Created workflow: {workflow.name} (ID: {workflow.id})")
            return workflow
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create workflow: {e}")
            raise
    
    async def _create_workflow_steps(self, workflow_id: int, steps_data: List[Dict]):
        """创建工作流步骤"""
        for i, step_data in enumerate(steps_data):
            step = WorkflowStep(
                workflow_id=workflow_id,
                name=step_data.get("name"),
                description=step_data.get("description"),
                step_type=step_data.get("type"),
                config=step_data.get("config"),
                order_index=i,
                position_x=step_data.get("position_x", 0),
                position_y=step_data.get("position_y", 0),
                condition=step_data.get("condition"),
                dependencies=step_data.get("dependencies")
            )
            self.db.add(step)
        
        self.db.commit()
    
    async def get_workflow_by_id(self, workflow_id: int) -> Optional[Workflow]:
        """根据ID获取工作流"""
        return self.db.query(Workflow).filter(
            and_(Workflow.id == workflow_id, Workflow.is_deleted == False)
        ).first()
    
    async def update_workflow(
        self, 
        workflow_id: int, 
        workflow_data: WorkflowUpdate, 
        user_id: int
    ) -> Optional[Workflow]:
        """更新工作流"""
        workflow = await self.get_workflow_by_id(workflow_id)
        if not workflow:
            return None
        
        try:
            # 更新基本信息
            update_data = workflow_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(workflow, field):
                    setattr(workflow, field, value)
            
            workflow.updated_at = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(workflow)
            
            logger.info(f"Updated workflow: {workflow.name} (ID: {workflow.id})")
            return workflow
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update workflow {workflow_id}: {e}")
            raise
    
    async def delete_workflow(self, workflow_id: int, user_id: int) -> bool:
        """删除工作流（软删除）"""
        workflow = await self.get_workflow_by_id(workflow_id)
        if not workflow:
            return False
        
        try:
            workflow.soft_delete()
            self.db.commit()
            
            logger.info(f"Deleted workflow: {workflow.name} (ID: {workflow.id})")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to delete workflow {workflow_id}: {e}")
            return False
    
    async def execute_workflow(
        self, 
        workflow_id: int, 
        execution_data: WorkflowExecutionCreate, 
        executor_id: int
    ) -> Optional[WorkflowExecution]:
        """执行工作流"""
        workflow = await self.get_workflow_by_id(workflow_id)
        if not workflow or workflow.status != WorkflowStatus.ACTIVE:
            return None
        
        try:
            execution = WorkflowExecution(
                workflow_id=workflow_id,
                execution_id=str(uuid.uuid4()),
                input_data=execution_data.input_data,
                context=execution_data.context or {},
                executor_id=executor_id,
                max_retries=workflow.max_retries,
                trigger_type=execution_data.trigger_type,
                trigger_data=execution_data.trigger_data
            )
            
            self.db.add(execution)
            self.db.commit()
            self.db.refresh(execution)
            
            # 开始执行
            execution.start_execution()
            self.db.commit()

            # 异步执行工作流
            await self._execute_workflow_async(execution)

            logger.info(f"Started workflow execution: {execution.execution_id}")
            return execution
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to execute workflow {workflow_id}: {e}")
            raise
    
    async def get_workflow_executions(
        self, 
        workflow_id: int, 
        skip: int = 0, 
        limit: int = 10
    ) -> List[WorkflowExecution]:
        """获取工作流执行记录"""
        return self.db.query(WorkflowExecution).filter(
            WorkflowExecution.workflow_id == workflow_id
        ).order_by(WorkflowExecution.created_at.desc()).offset(skip).limit(limit).all()
    
    async def get_execution_by_id(self, execution_id: str) -> Optional[WorkflowExecution]:
        """根据执行ID获取执行记录"""
        return self.db.query(WorkflowExecution).filter(
            WorkflowExecution.execution_id == execution_id
        ).first()
    
    async def cancel_execution(self, execution_id: str, user_id: int) -> bool:
        """取消执行"""
        execution = await self.get_execution_by_id(execution_id)
        if not execution or execution.status not in [ExecutionStatus.PENDING, ExecutionStatus.RUNNING]:
            return False
        
        try:
            execution.status = ExecutionStatus.CANCELLED
            execution.completed_at = datetime.utcnow()
            if execution.started_at:
                execution.duration_seconds = int(
                    (execution.completed_at - execution.started_at).total_seconds()
                )
            
            self.db.commit()
            
            logger.info(f"Cancelled workflow execution: {execution_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to cancel execution {execution_id}: {e}")
            return False
    
    async def activate_workflow(self, workflow_id: int, user_id: int) -> bool:
        """激活工作流"""
        workflow = await self.get_workflow_by_id(workflow_id)
        if not workflow:
            return False
        
        try:
            workflow.status = WorkflowStatus.ACTIVE
            workflow.updated_at = datetime.utcnow()
            self.db.commit()
            
            logger.info(f"Activated workflow: {workflow.name} (ID: {workflow.id})")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to activate workflow {workflow_id}: {e}")
            return False
    
    async def deactivate_workflow(self, workflow_id: int, user_id: int) -> bool:
        """停用工作流"""
        workflow = await self.get_workflow_by_id(workflow_id)
        if not workflow:
            return False
        
        try:
            workflow.status = WorkflowStatus.PAUSED
            workflow.updated_at = datetime.utcnow()
            self.db.commit()
            
            logger.info(f"Deactivated workflow: {workflow.name} (ID: {workflow.id})")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to deactivate workflow {workflow_id}: {e}")
            return False
    
    async def get_workflow_statistics(self, workflow_id: int) -> Optional[Dict[str, Any]]:
        """获取工作流统计信息"""
        workflow = await self.get_workflow_by_id(workflow_id)
        if not workflow:
            return None

        return {
            "workflow_id": workflow_id,
            "name": workflow.name,
            "status": workflow.status,
            "total_executions": workflow.execution_count,
            "successful_executions": workflow.success_count,
            "failed_executions": workflow.failure_count,
            "success_rate": workflow.get_success_rate(),
            "created_at": workflow.created_at,
            "updated_at": workflow.updated_at
        }

    async def _execute_workflow_async(self, execution: WorkflowExecution) -> None:
        """异步执行工作流"""
        try:
            # 获取工作流定义
            workflow = await self.get_workflow_by_id(execution.workflow_id)
            if not workflow:
                raise ValueError(f"Workflow not found: {execution.workflow_id}")

            # 解析工作流定义
            workflow_def = json.loads(workflow.definition) if isinstance(workflow.definition, str) else workflow.definition

            # 创建执行上下文
            execution_context = {
                "workflow_id": execution.workflow_id,
                "execution_id": execution.execution_id,
                "input_data": json.loads(execution.input_data) if isinstance(execution.input_data, str) else execution.input_data,
                "variables": {},
                "step_results": {}
            }

            # 获取工作流步骤
            steps = self.db.query(WorkflowStep).filter(
                WorkflowStep.workflow_id == execution.workflow_id
            ).order_by(WorkflowStep.order_index).all()

            # 执行工作流步骤
            result = await self._execute_steps(steps, execution_context)

            # 更新执行状态
            execution.status = ExecutionStatus.COMPLETED
            execution.output_data = json.dumps(result)
            execution.completed_at = datetime.utcnow()
            if execution.started_at:
                execution.duration_seconds = int(
                    (execution.completed_at - execution.started_at).total_seconds()
                )
            self.db.commit()

        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")
            execution.status = ExecutionStatus.FAILED
            execution.error_message = str(e)
            execution.completed_at = datetime.utcnow()
            if execution.started_at:
                execution.duration_seconds = int(
                    (execution.completed_at - execution.started_at).total_seconds()
                )
            self.db.commit()

    async def _execute_steps(self, steps: List[WorkflowStep], context: Dict[str, Any]) -> Dict[str, Any]:
        """执行工作流步骤"""
        for step in steps:
            step_name = step.name or f"step_{step.order_index}"
            step_type = step.step_type

            logger.info(f"Executing step: {step_name} ({step_type})")

            # 检查执行条件
            if not await self._check_step_condition(step, context):
                logger.info(f"Step {step_name} skipped due to condition")
                continue

            # 获取步骤处理器
            processor = self.step_processors.get(step_type)
            if not processor:
                logger.warning(f"Unknown step type: {step_type}, using default processor")
                processor = self._process_default_step

            # 执行步骤
            step_config = json.loads(step.config) if isinstance(step.config, str) else (step.config or {})
            step_result = await processor(step_config, context)
            context["step_results"][step_name] = step_result

            # 更新变量
            if step_config.get("output_variable"):
                context["variables"][step_config["output_variable"]] = step_result

        return context["step_results"]

    async def _check_step_condition(self, step: WorkflowStep, context: Dict[str, Any]) -> bool:
        """检查步骤执行条件"""
        condition = step.condition
        if not condition:
            return True

        try:
            # 替换变量
            for var_name, var_value in context["variables"].items():
                condition = condition.replace(f"{{{{{var_name}}}}}", str(var_value))

            # 简单的条件评估
            if "==" in condition:
                left, right = condition.split("==")
                return left.strip() == right.strip()
            elif "!=" in condition:
                left, right = condition.split("!=")
                return left.strip() != right.strip()
            elif ">" in condition:
                left, right = condition.split(">")
                return float(left.strip()) > float(right.strip())
            elif "<" in condition:
                left, right = condition.split("<")
                return float(left.strip()) < float(right.strip())
            else:
                return bool(condition)

        except Exception as e:
            logger.error(f"Condition evaluation failed: {e}")
            return True

    async def _process_input_step(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入步骤"""
        input_type = step_config.get("input_type", "data")

        if input_type == "file":
            return {
                "type": "file_input",
                "files": context["input_data"].get("files", []),
                "formats": step_config.get("formats", [])
            }
        elif input_type == "voice":
            return {
                "type": "voice_input",
                "audio_data": context["input_data"].get("audio_data", {}),
                "language": step_config.get("language", "auto")
            }
        else:
            return {
                "type": "data_input",
                "data": context["input_data"]
            }

    async def _process_processing_step(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理数据处理步骤"""
        processing_type = step_config.get("processing_type", "transform")
        input_data = self._get_step_input(step_config, context)

        if processing_type == "transform":
            return await self._transform_data(input_data, step_config.get("transform_rules", {}))
        elif processing_type == "validate":
            return await self._validate_data(input_data, step_config.get("validation_rules", {}))
        elif processing_type == "filter":
            return await self._filter_data(input_data, step_config.get("filter_rules", {}))
        else:
            return input_data

    async def _process_recognition_step(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理识别步骤"""
        recognition_type = step_config.get("recognition_type", "speech")
        input_data = self._get_step_input(step_config, context)

        recognition_request = {
            "recognition_type": recognition_type,
            "input_data": input_data,
            "config": step_config.get("config", {})
        }

        return await self.recognition_service.process_recognition(recognition_request)

    async def _process_decision_step(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理决策步骤"""
        decision_rules = step_config.get("rules", [])
        input_data = self._get_step_input(step_config, context)

        for rule in decision_rules:
            condition = rule.get("condition", "")
            if await self._evaluate_condition(condition, input_data, context):
                return {
                    "decision": rule.get("action", ""),
                    "rule_matched": rule.get("name", ""),
                    "confidence": rule.get("confidence", 1.0)
                }

        return {
            "decision": step_config.get("default_action", "continue"),
            "rule_matched": "default",
            "confidence": 0.5
        }

    async def _process_action_step(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理动作步骤"""
        action_type = step_config.get("action_type", "api_call")
        input_data = self._get_step_input(step_config, context)

        if action_type == "api_call":
            return await self._make_api_call(step_config.get("api_config", {}), input_data)
        elif action_type == "send_email":
            return await self._send_email(step_config.get("email_config", {}), input_data)
        elif action_type == "save_data":
            return await self._save_data(step_config.get("save_config", {}), input_data)
        elif action_type == "generate_contract":
            return await self.contract_service.process_voice_contract(input_data)
        else:
            return {"action": action_type, "status": "completed"}

    async def _process_output_step(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理输出步骤"""
        output_format = step_config.get("format", "json")
        input_data = self._get_step_input(step_config, context)

        if output_format == "json":
            return input_data
        elif output_format == "xml":
            return {"xml_data": self._convert_to_xml(input_data)}
        elif output_format == "csv":
            return {"csv_data": self._convert_to_csv(input_data)}
        else:
            return input_data

    async def _process_parallel_step(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理并行步骤"""
        branches = step_config.get("branches", [])
        tasks = []

        for branch in branches:
            # 为每个分支创建独立的上下文
            branch_context = context.copy()
            branch_steps = branch.get("steps", [])

            # 将步骤配置转换为WorkflowStep对象（模拟）
            mock_steps = []
            for i, step_data in enumerate(branch_steps):
                mock_step = type('MockStep', (), {
                    'name': step_data.get('name', f'branch_step_{i}'),
                    'step_type': step_data.get('type', 'processing'),
                    'config': json.dumps(step_data.get('config', {})),
                    'condition': step_data.get('condition'),
                    'order_index': i
                })()
                mock_steps.append(mock_step)

            task = asyncio.create_task(
                self._execute_steps(mock_steps, branch_context)
            )
            tasks.append((branch.get("name", f"branch_{len(tasks)}"), task))

        results = {}
        for branch_name, task in tasks:
            try:
                results[branch_name] = await task
            except Exception as e:
                logger.error(f"Parallel branch {branch_name} failed: {e}")
                results[branch_name] = {"error": str(e)}

        return results

    async def _process_loop_step(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理循环步骤"""
        loop_type = step_config.get("loop_type", "for")
        loop_steps_config = step_config.get("steps", [])
        results = []

        # 将步骤配置转换为WorkflowStep对象（模拟）
        mock_steps = []
        for i, step_data in enumerate(loop_steps_config):
            mock_step = type('MockStep', (), {
                'name': step_data.get('name', f'loop_step_{i}'),
                'step_type': step_data.get('type', 'processing'),
                'config': json.dumps(step_data.get('config', {})),
                'condition': step_data.get('condition'),
                'order_index': i
            })()
            mock_steps.append(mock_step)

        if loop_type == "for":
            items = self._get_step_input(step_config, context)
            if isinstance(items, list):
                for item in items:
                    loop_context = context.copy()
                    loop_context["loop_item"] = item
                    result = await self._execute_steps(mock_steps, loop_context)
                    results.append(result)
        elif loop_type == "while":
            max_iterations = step_config.get("max_iterations", 100)
            iteration = 0
            while iteration < max_iterations:
                # 创建模拟的WorkflowStep对象来检查条件
                mock_condition_step = type('MockStep', (), {
                    'condition': step_config.get('condition', 'false')
                })()

                if not await self._check_step_condition(mock_condition_step, context):
                    break
                result = await self._execute_steps(mock_steps, context)
                results.append(result)
                iteration += 1

        return {"loop_results": results, "iterations": len(results)}

    async def _process_default_step(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """默认步骤处理器"""
        return {
            "status": "completed",
            "message": "Default step processor executed",
            "config": step_config
        }

    def _get_step_input(self, step_config: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """获取步骤输入数据"""
        input_source = step_config.get("input", "")

        if input_source.startswith("{{") and input_source.endswith("}}"):
            # 变量引用
            var_name = input_source[2:-2].strip()
            if var_name in context["variables"]:
                return context["variables"][var_name]
            elif var_name in context["step_results"]:
                return context["step_results"][var_name]
            else:
                return context["input_data"]
        else:
            return context["input_data"]

    async def _transform_data(self, data: Any, rules: Dict[str, Any]) -> Dict[str, Any]:
        """数据转换"""
        # 简单的数据转换实现
        return {"transformed_data": data, "rules_applied": rules}

    async def _validate_data(self, data: Any, rules: Dict[str, Any]) -> Dict[str, Any]:
        """数据验证"""
        # 简单的数据验证实现
        return {"is_valid": True, "validation_errors": [], "data": data}

    async def _filter_data(self, data: Any, rules: Dict[str, Any]) -> Dict[str, Any]:
        """数据过滤"""
        # 简单的数据过滤实现
        return {"filtered_data": data, "filter_rules": rules}

    async def _evaluate_condition(self, condition: str, data: Any, context: Dict[str, Any]) -> bool:
        """评估条件"""
        # 简单的条件评估实现
        try:
            # 替换变量
            for var_name, var_value in context["variables"].items():
                condition = condition.replace(f"{{{{{var_name}}}}}", str(var_value))

            # 评估条件
            if "==" in condition:
                left, right = condition.split("==")
                return left.strip() == right.strip()
            elif "!=" in condition:
                left, right = condition.split("!=")
                return left.strip() != right.strip()
            elif ">" in condition:
                left, right = condition.split(">")
                return float(left.strip()) > float(right.strip())
            elif "<" in condition:
                left, right = condition.split("<")
                return float(left.strip()) < float(right.strip())
            else:
                return bool(condition)
        except Exception as e:
            logger.error(f"Condition evaluation failed: {e}")
            return True

    async def _make_api_call(self, config: Dict[str, Any], data: Any) -> Dict[str, Any]:
        """API调用"""
        # 模拟API调用
        await asyncio.sleep(0.1)
        return {"api_response": "success", "data": data, "url": config.get("url", "unknown")}

    async def _send_email(self, config: Dict[str, Any], data: Any) -> Dict[str, Any]:
        """发送邮件"""
        # 模拟邮件发送
        await asyncio.sleep(0.05)
        return {"email_sent": True, "recipient": config.get("to", "unknown")}

    async def _save_data(self, config: Dict[str, Any], data: Any) -> Dict[str, Any]:
        """保存数据"""
        # 模拟数据保存
        return {"saved": True, "location": config.get("location", "database")}

    def _convert_to_xml(self, data: Any) -> str:
        """转换为XML格式"""
        return f"<data>{str(data)}</data>"

    def _convert_to_csv(self, data: Any) -> str:
        """转换为CSV格式"""
        return f"data\n{str(data)}"
