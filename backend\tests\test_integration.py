"""
系统集成测试
"""

import pytest
import asyncio
import json
import base64
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.core.database import get_db
from app.core.config import settings
from app.models.base import BaseModel
from app.core.init_db import init_database
import tempfile
import os

# 测试数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

# 创建测试客户端
client = TestClient(app)


@pytest.fixture(scope="module")
def setup_database():
    """设置测试数据库"""
    BaseModel.metadata.create_all(bind=engine)
    yield
    BaseModel.metadata.drop_all(bind=engine)


@pytest.fixture
def auth_headers():
    """获取认证头"""
    # 创建测试用户并登录
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpass123",
        "full_name": "Test User"
    }
    
    # 注册用户
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 200
    
    # 登录获取token
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    response = client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200
    
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


class TestSystemIntegration:
    """系统集成测试类"""
    
    def test_health_check(self, setup_database):
        """测试健康检查"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_user_registration_and_login(self, setup_database):
        """测试用户注册和登录流程"""
        # 注册用户
        user_data = {
            "username": "integrationtest",
            "email": "<EMAIL>",
            "password": "password123",
            "full_name": "Integration Test User"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 200
        
        # 登录
        login_data = {
            "username": "integrationtest",
            "password": "password123"
        }
        response = client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        
        token_data = response.json()
        assert "access_token" in token_data
        assert token_data["token_type"] == "bearer"
    
    def test_speech_recognition_workflow(self, setup_database, auth_headers):
        """测试语音识别完整工作流"""
        # 创建模拟音频数据
        audio_data = b"fake audio data for testing"
        audio_base64 = base64.b64encode(audio_data).decode('utf-8')
        
        recognition_request = {
            "audio_base64": audio_base64,
            "format": "wav",
            "language": "zh-CN",
            "enable_emotion": True,
            "enable_speaker_id": False,
            "enable_timestamps": True
        }
        
        # 发送语音识别请求
        response = client.post(
            "/api/v1/recognition/speech/recognize",
            json=recognition_request,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        assert "task_id" in result
        assert result["recognition_type"] == "speech"
        assert result["status"] == "success"
    
    def test_vision_recognition_workflow(self, setup_database, auth_headers):
        """测试视觉识别完整工作流"""
        # 创建模拟图像数据
        image_data = b"fake image data for testing"
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        recognition_request = {
            "image_base64": image_base64,
            "format": "jpg",
            "enable_classification": True,
            "enable_detection": True,
            "enable_face_detection": False,
            "detection_threshold": 0.5
        }
        
        # 发送视觉识别请求
        response = client.post(
            "/api/v1/recognition/vision/analyze",
            json=recognition_request,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        assert "task_id" in result
        assert result["recognition_type"] == "vision"
        assert result["status"] == "success"
    
    def test_contract_management_workflow(self, setup_database, auth_headers):
        """测试合同管理完整工作流"""
        # 1. 创建语音合同录入请求
        audio_data = b"fake contract audio data"
        audio_base64 = base64.b64encode(audio_data).decode('utf-8')
        
        voice_contract_data = {
            "audio_base64": audio_base64,
            "format": "wav",
            "duration": 30,
            "timestamp": "2024-08-28T10:00:00Z"
        }
        
        # 发送语音合同处理请求
        response = client.post(
            "/api/v1/contracts/voice-contract",
            json=voice_contract_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "data" in result
        
        contract_data = result["data"]
        assert "transcript" in contract_data
        assert "contract_info" in contract_data
        assert "risk_assessment" in contract_data
        
        # 2. 获取合同列表
        response = client.get("/api/v1/contracts/list", headers=auth_headers)
        assert response.status_code == 200
        
        contracts_result = response.json()
        assert contracts_result["success"] is True
        assert "data" in contracts_result
    
    def test_workflow_execution(self, setup_database, auth_headers):
        """测试工作流执行"""
        # 创建简单工作流
        workflow_data = {
            "name": "测试工作流",
            "description": "集成测试工作流",
            "workflow_type": "test",
            "definition": {
                "steps": [
                    {
                        "id": "input_1",
                        "type": "input",
                        "name": "输入步骤",
                        "config": {"input_type": "data"}
                    },
                    {
                        "id": "output_1",
                        "type": "output",
                        "name": "输出步骤",
                        "config": {"format": "json"}
                    }
                ]
            }
        }
        
        # 创建工作流
        response = client.post(
            "/api/v1/workflows/",
            json=workflow_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        workflow_result = response.json()
        workflow_id = workflow_result["id"]
        
        # 执行工作流
        execution_data = {
            "input_data": {"test": "data"},
            "config": {}
        }
        
        response = client.post(
            f"/api/v1/workflows/{workflow_id}/execute",
            json=execution_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        execution_result = response.json()
        assert "execution_id" in execution_result
    
    def test_agent_management(self, setup_database, auth_headers):
        """测试智能体管理"""
        # 创建智能体
        agent_data = {
            "name": "测试智能体",
            "description": "用于集成测试的智能体",
            "agent_type": "recognition",
            "config": {
                "recognition_type": "speech",
                "model": "whisper-base"
            }
        }
        
        response = client.post(
            "/api/v1/agents/",
            json=agent_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        agent_result = response.json()
        agent_id = agent_result["id"]
        
        # 获取智能体详情
        response = client.get(f"/api/v1/agents/{agent_id}", headers=auth_headers)
        assert response.status_code == 200
        
        agent_detail = response.json()
        assert agent_detail["name"] == "测试智能体"
        assert agent_detail["agent_type"] == "recognition"
        
        # 更新智能体状态
        update_data = {"status": "active"}
        response = client.put(
            f"/api/v1/agents/{agent_id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
    
    def test_file_upload_recognition(self, setup_database, auth_headers):
        """测试文件上传识别"""
        # 创建临时测试文件
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp_file:
            tmp_file.write(b"This is a test document for recognition.")
            tmp_file_path = tmp_file.name
        
        try:
            # 上传文件进行识别
            with open(tmp_file_path, "rb") as f:
                files = {"file": ("test.txt", f, "text/plain")}
                response = client.post(
                    "/api/v1/recognition/upload?recognition_type=document",
                    files=files,
                    headers=auth_headers
                )
            
            assert response.status_code == 200
            result = response.json()
            assert "task_id" in result
            assert result["recognition_type"] == "document"
            
        finally:
            # 清理临时文件
            os.unlink(tmp_file_path)
    
    def test_monitoring_metrics(self, setup_database, auth_headers):
        """测试监控指标"""
        # 获取系统监控数据
        response = client.get("/api/v1/monitoring/metrics", headers=auth_headers)
        assert response.status_code == 200
        
        metrics = response.json()
        assert "system_metrics" in metrics
        assert "application_metrics" in metrics
    
    def test_error_handling(self, setup_database, auth_headers):
        """测试错误处理"""
        # 测试无效的识别请求
        invalid_request = {
            "invalid_field": "invalid_value"
        }
        
        response = client.post(
            "/api/v1/recognition/speech/recognize",
            json=invalid_request,
            headers=auth_headers
        )
        
        assert response.status_code == 422  # Validation error
        
        # 测试访问不存在的资源
        response = client.get("/api/v1/agents/99999", headers=auth_headers)
        assert response.status_code == 404
    
    def test_performance_metrics(self, setup_database, auth_headers):
        """测试性能指标"""
        # 发送多个并发请求测试性能
        import concurrent.futures
        import time
        
        def make_request():
            response = client.get("/health")
            return response.status_code == 200
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(20)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 验证所有请求都成功
        assert all(results)
        
        # 验证响应时间合理（20个请求在5秒内完成）
        assert duration < 5.0
        
        print(f"Performance test: 20 requests completed in {duration:.2f} seconds")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
