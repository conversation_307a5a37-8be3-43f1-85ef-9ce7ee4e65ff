#!/usr/bin/env python3
"""
数据库初始化启动脚本
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.init_db import init_database
from app.core.config import settings
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
import time

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def wait_for_database(max_retries: int = 30, retry_interval: int = 2):
    """等待数据库连接可用"""
    logger.info("Waiting for database connection...")
    
    for attempt in range(max_retries):
        try:
            engine = create_engine(settings.DATABASE_URL)
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("Database connection established")
            return True
        except OperationalError as e:
            logger.warning(f"Database connection attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_interval)
            else:
                logger.error("Failed to connect to database after maximum retries")
                return False
    
    return False


def create_database_if_not_exists():
    """如果数据库不存在则创建"""
    try:
        # 解析数据库URL
        from urllib.parse import urlparse
        parsed = urlparse(settings.DATABASE_URL)
        
        # 连接到默认数据库（通常是postgres）
        default_db_url = f"{parsed.scheme}://{parsed.netloc}/postgres"
        engine = create_engine(default_db_url)
        
        # 检查目标数据库是否存在
        db_name = parsed.path.lstrip('/')
        
        with engine.connect() as conn:
            # 设置自动提交模式
            conn.execute(text("COMMIT"))
            
            # 检查数据库是否存在
            result = conn.execute(
                text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
                {"db_name": db_name}
            )
            
            if not result.fetchone():
                logger.info(f"Creating database: {db_name}")
                conn.execute(text(f"CREATE DATABASE {db_name}"))
                logger.info(f"Database {db_name} created successfully")
            else:
                logger.info(f"Database {db_name} already exists")
                
    except Exception as e:
        logger.warning(f"Could not create database automatically: {e}")
        logger.info("Please ensure the database exists manually")


def run_migrations():
    """运行数据库迁移"""
    try:
        logger.info("Running database migrations...")
        
        # 使用alembic运行迁移
        import subprocess
        alembic_cmd = [
            sys.executable, "-m", "alembic", 
            "-c", str(project_root / "alembic.ini"),
            "upgrade", "head"
        ]
        
        result = subprocess.run(
            alembic_cmd,
            cwd=str(project_root),
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.info("Database migrations completed successfully")
            logger.debug(f"Migration output: {result.stdout}")
        else:
            logger.error(f"Migration failed: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Failed to run migrations: {e}")
        return False
    
    return True


def main():
    """主函数"""
    logger.info("Starting database initialization...")
    
    # 检查环境变量
    if not settings.DATABASE_URL:
        logger.error("DATABASE_URL environment variable is not set")
        sys.exit(1)
    
    logger.info(f"Database URL: {settings.DATABASE_URL}")
    
    try:
        # 1. 创建数据库（如果不存在）
        if settings.DATABASE_URL.startswith("postgresql"):
            create_database_if_not_exists()
        
        # 2. 等待数据库连接
        if not wait_for_database():
            logger.error("Could not establish database connection")
            sys.exit(1)
        
        # 3. 运行迁移
        if not run_migrations():
            logger.error("Database migration failed")
            sys.exit(1)
        
        # 4. 初始化数据
        logger.info("Initializing database data...")
        init_database()
        
        logger.info("Database initialization completed successfully!")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
