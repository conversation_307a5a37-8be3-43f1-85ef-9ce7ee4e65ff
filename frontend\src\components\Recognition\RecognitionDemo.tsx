import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Tab,
  Tabs,
  Alert,
  CircularProgress,
  Chip,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  LinearProgress
} from '@mui/material';
import {
  <PERSON><PERSON>,
  <PERSON>c<PERSON><PERSON>,
  PhotoCamera,
  Description,
  Fingerprint,
  CloudUpload,
  PlayArrow,
  Stop,
  Visibility,
  Download,
  Settings,
  CheckCircle,
  Error,
  Info
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import { recognitionApi } from '../../services/api';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`recognition-tabpanel-${index}`}
      aria-labelledby={`recognition-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface RecognitionResult {
  taskId: string;
  recognitionType: string;
  status: string;
  result: any;
  confidence?: number;
  processingTime?: number;
  timestamp: string;
}

const RecognitionDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState<RecognitionResult[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [configDialog, setConfigDialog] = useState(false);
  const [config, setConfig] = useState({
    speech: {
      language: 'zh-CN',
      enableEmotion: false,
      enableSpeakerId: false,
      enableTimestamps: false
    },
    vision: {
      enableClassification: true,
      enableDetection: false,
      enableFaceDetection: false,
      enableOcr: false,
      detectionThreshold: 0.5
    },
    document: {
      documentType: 'auto',
      enableTableExtraction: false,
      enableSealDetection: false,
      language: 'auto'
    },
    biometric: {
      biometricType: 'face',
      operation: 'identify',
      qualityThreshold: 0.7,
      matchThreshold: 0.8
    }
  });

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setSelectedFile(null);
    setPreviewUrl(null);
  };

  // 语音录制功能
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        await processAudioRecognition(audioBlob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('无法访问麦克风，请检查权限设置');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const processAudioRecognition = async (audioBlob: Blob) => {
    setIsProcessing(true);
    try {
      const reader = new FileReader();
      reader.onload = async () => {
        const base64Audio = (reader.result as string).split(',')[1];
        
        const response = await recognitionApi.speechRecognize({
          audio_base64: base64Audio,
          format: 'wav',
          ...config.speech
        });

        addResult(response);
      };
      reader.readAsDataURL(audioBlob);
    } catch (error) {
      console.error('Speech recognition failed:', error);
      alert('语音识别失败，请重试');
    } finally {
      setIsProcessing(false);
    }
  };

  // 文件上传处理
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setSelectedFile(file);
      
      // 创建预览URL
      if (file.type.startsWith('image/')) {
        const url = URL.createObjectURL(file);
        setPreviewUrl(url);
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: getAcceptedFileTypes(),
    maxSize: 50 * 1024 * 1024, // 50MB
    multiple: false
  });

  function getAcceptedFileTypes() {
    switch (activeTab) {
      case 0: // Speech
        return {
          'audio/*': ['.wav', '.mp3', '.m4a', '.flac']
        };
      case 1: // Vision
        return {
          'image/*': ['.jpg', '.jpeg', '.png', '.bmp', '.gif']
        };
      case 2: // Document
        return {
          'application/pdf': ['.pdf'],
          'image/*': ['.jpg', '.jpeg', '.png'],
          'application/msword': ['.doc'],
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
        };
      case 3: // Biometric
        return {
          'image/*': ['.jpg', '.jpeg', '.png', '.bmp']
        };
      default:
        return {};
    }
  }

  const processFileRecognition = async () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      
      let recognitionType = '';
      let apiCall;
      
      switch (activeTab) {
        case 0: // Speech
          recognitionType = 'speech';
          apiCall = recognitionApi.uploadAndRecognize(formData, 'speech', JSON.stringify(config.speech));
          break;
        case 1: // Vision
          recognitionType = 'vision';
          apiCall = recognitionApi.uploadAndRecognize(formData, 'vision', JSON.stringify(config.vision));
          break;
        case 2: // Document
          recognitionType = 'document';
          apiCall = recognitionApi.uploadAndRecognize(formData, 'document', JSON.stringify(config.document));
          break;
        case 3: // Biometric
          recognitionType = 'biometric';
          apiCall = recognitionApi.uploadAndRecognize(formData, 'biometric', JSON.stringify(config.biometric));
          break;
        default:
          throw new Error('Unknown recognition type');
      }

      const response = await apiCall;
      addResult(response);
      
    } catch (error) {
      console.error('File recognition failed:', error);
      alert('文件识别失败，请重试');
    } finally {
      setIsProcessing(false);
    }
  };

  const addResult = (result: RecognitionResult) => {
    setResults(prev => [result, ...prev]);
  };

  const renderSpeechTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              实时语音识别
            </Typography>
            <Box sx={{ textAlign: 'center', py: 3 }}>
              <IconButton
                size="large"
                color={isRecording ? "secondary" : "primary"}
                onClick={isRecording ? stopRecording : startRecording}
                disabled={isProcessing}
                sx={{ 
                  width: 80, 
                  height: 80,
                  border: 2,
                  borderColor: isRecording ? 'secondary.main' : 'primary.main'
                }}
              >
                {isRecording ? <MicOff sx={{ fontSize: 40 }} /> : <Mic sx={{ fontSize: 40 }} />}
              </IconButton>
              <Typography variant="body2" sx={{ mt: 2 }}>
                {isRecording ? '点击停止录音' : '点击开始录音'}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              音频文件上传
            </Typography>
            <Box {...getRootProps()} sx={{
              border: '2px dashed',
              borderColor: isDragActive ? 'primary.main' : 'grey.300',
              borderRadius: 2,
              p: 3,
              textAlign: 'center',
              cursor: 'pointer',
              bgcolor: isDragActive ? 'action.hover' : 'background.paper'
            }}>
              <input {...getInputProps()} />
              <CloudUpload sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
              <Typography>
                {isDragActive ? '释放文件到这里' : '拖拽音频文件到这里，或点击选择'}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                支持 WAV, MP3, M4A, FLAC 格式
              </Typography>
            </Box>
            {selectedFile && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  已选择: {selectedFile.name}
                </Typography>
                <Button
                  variant="contained"
                  onClick={processFileRecognition}
                  disabled={isProcessing}
                  sx={{ mt: 1 }}
                >
                  开始识别
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderVisionTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              图像上传识别
            </Typography>
            <Box {...getRootProps()} sx={{
              border: '2px dashed',
              borderColor: isDragActive ? 'primary.main' : 'grey.300',
              borderRadius: 2,
              p: 3,
              textAlign: 'center',
              cursor: 'pointer',
              bgcolor: isDragActive ? 'action.hover' : 'background.paper',
              minHeight: 200
            }}>
              <input {...getInputProps()} />
              {previewUrl ? (
                <img 
                  src={previewUrl} 
                  alt="Preview" 
                  style={{ maxWidth: '100%', maxHeight: 150 }}
                />
              ) : (
                <>
                  <PhotoCamera sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
                  <Typography>
                    {isDragActive ? '释放图片到这里' : '拖拽图片到这里，或点击选择'}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    支持 JPG, PNG, BMP, GIF 格式
                  </Typography>
                </>
              )}
            </Box>
            {selectedFile && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  已选择: {selectedFile.name}
                </Typography>
                <Button
                  variant="contained"
                  onClick={processFileRecognition}
                  disabled={isProcessing}
                  sx={{ mt: 1 }}
                >
                  开始识别
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              识别配置
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={config.vision.enableClassification}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    vision: { ...prev.vision, enableClassification: e.target.checked }
                  }))}
                />
              }
              label="图像分类"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={config.vision.enableDetection}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    vision: { ...prev.vision, enableDetection: e.target.checked }
                  }))}
                />
              }
              label="目标检测"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={config.vision.enableFaceDetection}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    vision: { ...prev.vision, enableFaceDetection: e.target.checked }
                  }))}
                />
              }
              label="人脸检测"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={config.vision.enableOcr}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    vision: { ...prev.vision, enableOcr: e.target.checked }
                  }))}
                />
              }
              label="文字识别"
            />
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderDocumentTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              文档识别上传
            </Typography>
            <Box {...getRootProps()} sx={{
              border: '2px dashed',
              borderColor: isDragActive ? 'primary.main' : 'grey.300',
              borderRadius: 2,
              p: 3,
              textAlign: 'center',
              cursor: 'pointer',
              bgcolor: isDragActive ? 'action.hover' : 'background.paper'
            }}>
              <input {...getInputProps()} />
              <Description sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
              <Typography>
                {isDragActive ? '释放文档到这里' : '拖拽文档到这里，或点击选择'}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                支持 PDF, DOC, DOCX, JPG, PNG 格式
              </Typography>
            </Box>
            {selectedFile && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  已选择: {selectedFile.name}
                </Typography>
                <Button
                  variant="contained"
                  onClick={processFileRecognition}
                  disabled={isProcessing}
                  sx={{ mt: 1 }}
                >
                  开始识别
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderBiometricTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              生物特征识别
            </Typography>
            <Box {...getRootProps()} sx={{
              border: '2px dashed',
              borderColor: isDragActive ? 'primary.main' : 'grey.300',
              borderRadius: 2,
              p: 3,
              textAlign: 'center',
              cursor: 'pointer',
              bgcolor: isDragActive ? 'action.hover' : 'background.paper'
            }}>
              <input {...getInputProps()} />
              <Fingerprint sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
              <Typography>
                {isDragActive ? '释放生物特征图片到这里' : '拖拽生物特征图片到这里，或点击选择'}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                支持人脸、指纹、虹膜等生物特征图片
              </Typography>
            </Box>
            {selectedFile && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  已选择: {selectedFile.name}
                </Typography>
                <Button
                  variant="contained"
                  onClick={processFileRecognition}
                  disabled={isProcessing}
                  sx={{ mt: 1 }}
                >
                  开始识别
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderResults = () => (
    <Card sx={{ mt: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          识别结果
        </Typography>
        {isProcessing && (
          <Box sx={{ mb: 2 }}>
            <LinearProgress />
            <Typography variant="body2" sx={{ mt: 1 }}>
              正在处理中...
            </Typography>
          </Box>
        )}
        {results.length === 0 ? (
          <Typography color="textSecondary">
            暂无识别结果
          </Typography>
        ) : (
          <List>
            {results.map((result, index) => (
              <ListItem key={index} divider>
                <ListItemIcon>
                  {result.status === 'success' ? (
                    <CheckCircle color="success" />
                  ) : (
                    <Error color="error" />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle2">
                        {result.recognitionType} 识别
                      </Typography>
                      <Chip 
                        label={result.status} 
                        size="small" 
                        color={result.status === 'success' ? 'success' : 'error'}
                      />
                      {result.confidence && (
                        <Chip 
                          label={`置信度: ${(result.confidence * 100).toFixed(1)}%`} 
                          size="small" 
                          variant="outlined"
                        />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="textSecondary">
                        任务ID: {result.taskId}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        时间: {new Date(result.timestamp).toLocaleString()}
                      </Typography>
                      {result.processingTime && (
                        <Typography variant="body2" color="textSecondary">
                          处理时间: {result.processingTime.toFixed(2)}秒
                        </Typography>
                      )}
                      <Paper sx={{ p: 2, mt: 1, bgcolor: 'grey.50' }}>
                        <pre style={{ margin: 0, fontSize: '0.875rem', whiteSpace: 'pre-wrap' }}>
                          {JSON.stringify(result.result, null, 2)}
                        </pre>
                      </Paper>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        )}
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h4" gutterBottom>
        多模态AI识别演示
      </Typography>
      
      <Paper sx={{ width: '100%' }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="语音识别" icon={<Mic />} />
          <Tab label="视觉识别" icon={<PhotoCamera />} />
          <Tab label="文档识别" icon={<Description />} />
          <Tab label="生物特征" icon={<Fingerprint />} />
        </Tabs>
        
        <TabPanel value={activeTab} index={0}>
          {renderSpeechTab()}
        </TabPanel>
        
        <TabPanel value={activeTab} index={1}>
          {renderVisionTab()}
        </TabPanel>
        
        <TabPanel value={activeTab} index={2}>
          {renderDocumentTab()}
        </TabPanel>
        
        <TabPanel value={activeTab} index={3}>
          {renderBiometricTab()}
        </TabPanel>
      </Paper>
      
      {renderResults()}
    </Box>
  );
};

export default RecognitionDemo;
