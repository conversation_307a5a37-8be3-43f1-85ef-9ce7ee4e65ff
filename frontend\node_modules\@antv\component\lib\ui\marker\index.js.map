{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/ui/marker/index.ts"], "names": [], "mappings": ";;;;AAAA,mCAAwC;AACxC,mCAAuC;AAEvC,mCAA4C;AAC5C,mCAsBkB;AAElB,iCAAsC;AAItC,SAAS,OAAO,CAAC,MAAkC;IACjD,IAAM,UAAU,GAAG,IAAA,mBAAW,EAAC,MAAM,CAAC,CAAC;IAEvC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QACpD,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,IAAI,MAAM,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;QACtC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;IAA4B,kCAA2B;IAAvD;;IAsDA,CAAC;IArDQ,uBAAM,GAAb,UAAc,UAAsC,EAAE,SAAgB;QAC5D,IAAA,KAAiB,UAAU,EAAtB,EAAL,CAAC,mBAAG,CAAC,KAAA,EAAE,KAAU,UAAU,EAAf,EAAL,CAAC,mBAAG,CAAC,KAAA,CAAgB;QACpC,IAAM,KAAkC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAjE,MAAM,YAAA,EAAE,YAAS,EAAT,IAAI,mBAAG,EAAE,KAAA,EAAK,KAAK,sBAA7B,kBAA+B,CAAoC,CAAC;QAE1E,IAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAA,aAAM,EAAC,CAAC,CAAC,IAAI,EAAE,IAAA,aAAM,EAAC,SAAS,CAAC,EAAE,UAAC,KAAK;YACtC,KAAK;iBACF,sBAAsB,CAAC,QAAQ,EAAE,IAAK,CAAC;iBACvC,IAAI,CAAC,WAAW,EAAE,iBAAU,IAAI,YAAS,CAAC;iBAC1C,IAAI,CAAC,UAAC,SAAS;gBACd,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;oBACrB,4BAA4B;oBAC5B,IAAM,CAAC,GAAI,IAAe,GAAG,CAAC,CAAC;oBAC/B,SAAS,CAAC,MAAM,CAAC;wBACf,GAAG,EAAE,MAAM;wBACX,KAAK,EAAE,CAAC;wBACR,MAAM,EAAE,CAAC;wBACT,CAAC,EAAE,CAAC,GAAG,IAAI;wBACX,CAAC,EAAE,CAAC,GAAG,IAAI;qBACZ,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,IAAM,CAAC,GAAI,IAAe,GAAG,CAAC,CAAC;oBAC/B,IAAM,QAAQ,GAAG,IAAA,iBAAU,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBACxE,SAAS,CAAC,MAAM,oBAAG,CAAC,EAAE,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAK,KAAK,EAAG,CAAC;gBACzD,CAAC;YACH,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAEc,wBAAiB,GAAG,IAAI,GAAG,EAA4B,CAAC;IAEvE;;;;OAIG;IACW,qBAAc,GAAG,UAAC,IAAY,EAAE,MAAwB;QACpE,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEF;;OAEG;IACW,gBAAS,GAAG,UAAC,IAAY;QACrC,OAAO,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC,CAAC;IAEF;;OAEG;IACW,iBAAU,GAAG;QACzB,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC;IACrD,CAAC,CAAC;IACJ,aAAC;CAAA,AAtDD,CAA4B,gBAAS,GAsDpC;AAtDY,wBAAM;AAwDnB,gCAAgC;AAChC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,cAAK,CAAC,CAAC;AACtC,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,eAAM,CAAC,CAAC;AACxC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,aAAI,CAAC,CAAC;AACpC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,aAAI,CAAC,CAAC;AACpC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,aAAI,CAAC,CAAC;AAEpC,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,eAAM,CAAC,CAAC;AACxC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,cAAK,CAAC,CAAC;AACtC,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,eAAM,CAAC,CAAC;AACxC,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,gBAAO,CAAC,CAAC;AAC1C,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,eAAM,CAAC,CAAC;AACxC,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,gBAAO,CAAC,CAAC;AAC1C,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,iBAAQ,CAAC,CAAC;AAC5C,MAAM,CAAC,cAAc,CAAC,eAAe,EAAE,qBAAY,CAAC,CAAC;AACrD,kBAAkB;AAClB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,aAAI,CAAC,CAAC;AACpC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,YAAG,CAAC,CAAC;AAClC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,aAAI,CAAC,CAAC;AACpC,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,eAAM,CAAC,CAAC;AACxC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,WAAE,CAAC,CAAC;AAChC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,WAAE,CAAC,CAAC;AAChC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,YAAG,CAAC,CAAC;AAClC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,YAAG,CAAC,CAAC;AAClC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,cAAK,CAAC,CAAC"}