"""
多模态识别相关数据验证模式
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

from app.models.recognition import RecognitionType, RecognitionStatus


class RecognitionRequest(BaseModel):
    """识别请求模式"""
    task_id: Optional[str] = Field(None, description="任务ID，如果不提供将自动生成")
    recognition_type: RecognitionType = Field(..., description="识别类型")
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    config: Optional[Dict[str, Any]] = Field(None, description="识别配置")
    priority: Optional[int] = Field(1, ge=1, le=10, description="优先级（1-10）")
    
    @validator('input_data')
    def validate_input_data(cls, v, values):
        """验证输入数据"""
        recognition_type = values.get('recognition_type')
        
        if recognition_type == RecognitionType.SPEECH:
            if 'audio_base64' not in v and 'audio_url' not in v:
                raise ValueError('Speech recognition requires audio_base64 or audio_url')
        elif recognition_type == RecognitionType.VISION:
            if 'image_base64' not in v and 'image_url' not in v:
                raise ValueError('Vision recognition requires image_base64 or image_url')
        elif recognition_type == RecognitionType.DOCUMENT:
            if 'document_base64' not in v and 'document_url' not in v:
                raise ValueError('Document recognition requires document_base64 or document_url')
        elif recognition_type == RecognitionType.BIOMETRIC:
            if 'data_base64' not in v and 'data_url' not in v:
                raise ValueError('Biometric recognition requires data_base64 or data_url')
        
        return v


class RecognitionResponse(BaseModel):
    """识别响应模式"""
    task_id: str = Field(..., description="任务ID")
    recognition_type: RecognitionType = Field(..., description="识别类型")
    status: str = Field(..., description="处理状态")
    result: Dict[str, Any] = Field(..., description="识别结果")
    confidence: Optional[float] = Field(None, ge=0, le=1, description="置信度")
    processing_time: Optional[float] = Field(None, ge=0, description="处理时间（秒）")
    error_message: Optional[str] = Field(None, description="错误信息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")
    
    class Config:
        from_attributes = True


class SpeechRecognitionRequest(BaseModel):
    """语音识别请求"""
    audio_base64: Optional[str] = Field(None, description="Base64编码的音频数据")
    audio_url: Optional[str] = Field(None, description="音频文件URL")
    format: Optional[str] = Field("wav", description="音频格式")
    sample_rate: Optional[int] = Field(16000, description="采样率")
    language: Optional[str] = Field("auto", description="语言代码")
    enable_emotion: Optional[bool] = Field(False, description="是否启用情感识别")
    enable_speaker_id: Optional[bool] = Field(False, description="是否启用声纹识别")
    enable_timestamps: Optional[bool] = Field(False, description="是否返回时间戳")


class VisionRecognitionRequest(BaseModel):
    """视觉识别请求"""
    image_base64: Optional[str] = Field(None, description="Base64编码的图像数据")
    image_url: Optional[str] = Field(None, description="图像文件URL")
    format: Optional[str] = Field("jpg", description="图像格式")
    enable_classification: Optional[bool] = Field(True, description="是否启用图像分类")
    enable_detection: Optional[bool] = Field(False, description="是否启用目标检测")
    enable_face_detection: Optional[bool] = Field(False, description="是否启用人脸检测")
    enable_ocr: Optional[bool] = Field(False, description="是否启用OCR")
    detection_threshold: Optional[float] = Field(0.5, ge=0, le=1, description="检测阈值")


class DocumentRecognitionRequest(BaseModel):
    """文档识别请求"""
    document_base64: Optional[str] = Field(None, description="Base64编码的文档数据")
    document_url: Optional[str] = Field(None, description="文档文件URL")
    format: Optional[str] = Field("pdf", description="文档格式")
    document_type: Optional[str] = Field("auto", description="文档类型")
    enable_table_extraction: Optional[bool] = Field(False, description="是否启用表格提取")
    enable_seal_detection: Optional[bool] = Field(False, description="是否启用印章检测")
    language: Optional[str] = Field("auto", description="文档语言")
    output_format: Optional[str] = Field("json", description="输出格式")


class BiometricRecognitionRequest(BaseModel):
    """生物特征识别请求"""
    data_base64: Optional[str] = Field(None, description="Base64编码的生物特征数据")
    data_url: Optional[str] = Field(None, description="生物特征数据URL")
    biometric_type: str = Field(..., description="生物特征类型（face/fingerprint/iris）")
    operation: str = Field("identify", description="操作类型（identify/verify/enroll）")
    template_id: Optional[str] = Field(None, description="模板ID（用于验证）")
    quality_threshold: Optional[float] = Field(0.7, ge=0, le=1, description="质量阈值")
    match_threshold: Optional[float] = Field(0.8, ge=0, le=1, description="匹配阈值")


class RecognitionTaskResponse(BaseModel):
    """识别任务响应"""
    id: int
    task_id: str
    recognition_type: RecognitionType
    status: RecognitionStatus
    confidence_score: Optional[float]
    processing_time: Optional[float]
    error_message: Optional[str]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class RecognitionResultResponse(BaseModel):
    """识别结果响应"""
    id: int
    task_id: str
    recognition_type: RecognitionType
    result_data: Dict[str, Any]
    confidence_score: Optional[float]
    processing_time: Optional[float]
    created_at: datetime
    
    class Config:
        from_attributes = True


class BatchRecognitionRequest(BaseModel):
    """批量识别请求"""
    batch_name: Optional[str] = Field(None, description="批次名称")
    recognition_type: RecognitionType = Field(..., description="识别类型")
    tasks: List[Dict[str, Any]] = Field(..., min_items=1, description="任务列表")
    config: Optional[Dict[str, Any]] = Field(None, description="批次配置")
    priority: Optional[int] = Field(1, ge=1, le=10, description="优先级")


class BatchRecognitionResponse(BaseModel):
    """批量识别响应"""
    batch_id: str = Field(..., description="批次ID")
    batch_name: Optional[str] = Field(None, description="批次名称")
    total_tasks: int = Field(..., description="总任务数")
    status: str = Field(..., description="批次状态")
    created_at: datetime = Field(..., description="创建时间")


class RecognitionModelResponse(BaseModel):
    """识别模型响应"""
    id: int
    model_name: str
    model_type: RecognitionType
    version: str
    description: Optional[str]
    capabilities: Optional[Dict[str, Any]]
    accuracy: Optional[float]
    deployment_status: str
    usage_count: int
    last_used_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True


class RecognitionStatistics(BaseModel):
    """识别统计信息"""
    total_tasks: int = Field(..., description="总任务数")
    completed_tasks: int = Field(..., description="已完成任务数")
    failed_tasks: int = Field(..., description="失败任务数")
    average_confidence: Optional[float] = Field(None, description="平均置信度")
    average_processing_time: Optional[float] = Field(None, description="平均处理时间")
    success_rate: float = Field(..., description="成功率")
    tasks_by_type: Dict[str, int] = Field(..., description="按类型分组的任务数")
    tasks_by_status: Dict[str, int] = Field(..., description="按状态分组的任务数")


class RecognitionHistoryResponse(BaseModel):
    """识别历史响应"""
    tasks: List[RecognitionTaskResponse] = Field(..., description="任务列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页")
    size: int = Field(..., description="页大小")
    statistics: RecognitionStatistics = Field(..., description="统计信息")


class ModelPerformanceMetrics(BaseModel):
    """模型性能指标"""
    model_name: str = Field(..., description="模型名称")
    accuracy: Optional[float] = Field(None, description="准确率")
    precision: Optional[float] = Field(None, description="精确率")
    recall: Optional[float] = Field(None, description="召回率")
    f1_score: Optional[float] = Field(None, description="F1分数")
    average_processing_time: Optional[float] = Field(None, description="平均处理时间")
    throughput: Optional[float] = Field(None, description="吞吐量（任务/秒）")
    error_rate: Optional[float] = Field(None, description="错误率")
    last_updated: datetime = Field(..., description="最后更新时间")


class RecognitionConfigUpdate(BaseModel):
    """识别配置更新"""
    model_configs: Optional[Dict[str, Dict[str, Any]]] = Field(None, description="模型配置")
    default_thresholds: Optional[Dict[str, float]] = Field(None, description="默认阈值")
    processing_limits: Optional[Dict[str, int]] = Field(None, description="处理限制")
    quality_settings: Optional[Dict[str, Any]] = Field(None, description="质量设置")


class RecognitionHealthCheck(BaseModel):
    """识别服务健康检查"""
    status: str = Field(..., description="服务状态")
    models_loaded: Dict[str, bool] = Field(..., description="已加载的模型")
    processing_queue_size: int = Field(..., description="处理队列大小")
    active_tasks: int = Field(..., description="活跃任务数")
    system_resources: Dict[str, Any] = Field(..., description="系统资源使用情况")
    last_check: datetime = Field(..., description="最后检查时间")


class RecognitionErrorResponse(BaseModel):
    """识别错误响应"""
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误信息")
    error_details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    task_id: Optional[str] = Field(None, description="任务ID")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")
    
    class Config:
        from_attributes = True
