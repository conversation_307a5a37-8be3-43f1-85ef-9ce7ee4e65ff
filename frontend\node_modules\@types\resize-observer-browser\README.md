# Installation
> `npm install --save @types/resize-observer-browser`

# Summary
This package contains type definitions for resize-observer-browser (https://developer.mozilla.org/en-US/docs/Web/API/ResizeObserver).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/resize-observer-browser.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/resize-observer-browser/index.d.ts)
````ts
interface Window {
    ResizeObserver: typeof ResizeObserver;
}

interface ResizeObserverOptions {
    /**
     * Sets which box model the observer will observe changes to. Possible values
     * are `content-box` (the default), and `border-box`.
     *
     * @default 'content-box'
     */
    box?: "content-box" | "border-box" | "device-pixel-content-box" | undefined;
}

interface ResizeObserverSize {
    readonly inlineSize: number;
    readonly blockSize: number;
}

interface ResizeObserver {
    disconnect(): void;
    observe(target: Element, options?: ResizeObserverOptions): void;
    unobserve(target: Element): void;
}

declare var ResizeObserver: {
    new(callback: ResizeObserverCallback): ResizeObserver;
    prototype: ResizeObserver;
};

interface ResizeObserverCallback {
    (entries: ResizeObserverEntry[], observer: ResizeObserver): void;
}

interface ResizeObserverEntry {
    readonly target: Element;
    readonly contentRect: DOMRectReadOnly;
    readonly borderBoxSize: readonly ResizeObserverSize[];
    readonly contentBoxSize: readonly ResizeObserverSize[];
    readonly devicePixelContentBoxSize: readonly ResizeObserverSize[];
}

````

### Additional Details
 * Last updated: Mon, 20 Nov 2023 23:36:24 GMT
 * Dependencies: none

# Credits
These definitions were written by [Chives](https://github.com/chivesrs), [William Furr](https://github.com/wffurr), and [Alexander Shushunov](https://github.com/AlexanderShushunov).
