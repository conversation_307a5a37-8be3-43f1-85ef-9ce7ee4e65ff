/**
 * 前端集成测试
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import userEvent from '@testing-library/user-event';
import { rest } from 'msw';
import { setupServer } from 'msw/node';

import { store } from '../store';
import { theme } from '../theme';
import App from '../App';
import RecognitionDemo from '../components/Recognition/RecognitionDemo';
import VoiceContractRecorder from '../components/Contract/VoiceContractRecorder';
import ContractList from '../components/Contract/ContractList';
import WorkflowDesigner from '../components/Workflow/WorkflowDesigner';

// Mock API服务器
const server = setupServer(
  // 认证相关API
  rest.post('/api/v1/auth/login', (req, res, ctx) => {
    return res(
      ctx.json({
        access_token: 'mock-token',
        token_type: 'bearer',
        user: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          full_name: 'Test User'
        }
      })
    );
  }),

  // 识别相关API
  rest.post('/api/v1/recognition/speech/recognize', (req, res, ctx) => {
    return res(
      ctx.json({
        task_id: 'speech_test_123',
        recognition_type: 'speech',
        status: 'success',
        result: {
          transcript: '这是一段测试语音识别结果',
          language: 'zh-CN',
          confidence: 0.95,
          duration: 5.2
        },
        confidence: 0.95,
        processing_time: 1.2,
        timestamp: new Date().toISOString()
      })
    );
  }),

  rest.post('/api/v1/recognition/vision/analyze', (req, res, ctx) => {
    return res(
      ctx.json({
        task_id: 'vision_test_123',
        recognition_type: 'vision',
        status: 'success',
        result: {
          classification: {
            category: 'object',
            label: 'computer',
            confidence: 0.92
          },
          detection: [
            {
              label: 'person',
              confidence: 0.95,
              bbox: [100, 50, 200, 300]
            }
          ]
        },
        confidence: 0.92,
        processing_time: 0.8,
        timestamp: new Date().toISOString()
      })
    );
  }),

  // 合同相关API
  rest.post('/api/v1/contracts/voice-contract', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        data: {
          transcript: '客户名称是北京科技公司，联系人张经理，合同金额10万元',
          contract_info: {
            customerName: '北京科技公司',
            contactPerson: '张经理',
            phone: '010-12345678',
            email: '<EMAIL>',
            contractType: '服务合同',
            amount: 100000,
            currency: 'CNY',
            contractNumber: 'CT20240828001',
            isComplete: true,
            missingFields: []
          },
          risk_assessment: {
            riskLevel: 'low',
            riskScore: 0.3,
            recommendations: ['标准审核流程', '确认客户资质'],
            isNewCustomer: false
          },
          contract_draft: '服务合同草稿内容...',
          status: 'draft_generated',
          nextSteps: ['发送合同确认邮件', '等待客户确认']
        }
      })
    );
  }),

  rest.get('/api/v1/contracts/list', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        data: {
          contracts: [
            {
              id: 1,
              uuid: 'contract-uuid-1',
              contractNumber: 'CT20240828001',
              customerName: '北京科技公司',
              amount: 100000,
              status: 'draft',
              createdAt: '2024-08-28T10:00:00Z'
            }
          ],
          total: 1,
          page: 1,
          size: 20
        }
      })
    );
  }),

  // 工作流相关API
  rest.post('/api/v1/workflows/', (req, res, ctx) => {
    return res(
      ctx.json({
        id: 1,
        uuid: 'workflow-uuid-1',
        name: '测试工作流',
        status: 'active',
        created_at: new Date().toISOString()
      })
    );
  }),

  rest.post('/api/v1/workflows/:id/execute', (req, res, ctx) => {
    return res(
      ctx.json({
        execution_id: 'exec_123',
        workflow_id: 1,
        status: 'completed',
        result: { success: true },
        execution_time: 2.5
      })
    );
  })
);

// 测试工具函数
const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

// 模拟文件上传
const createMockFile = (name: string, type: string, content: string = 'mock content') => {
  const file = new File([content], name, { type });
  return file;
};

// 模拟媒体设备
const mockMediaDevices = () => {
  Object.defineProperty(navigator, 'mediaDevices', {
    writable: true,
    value: {
      getUserMedia: jest.fn().mockResolvedValue({
        getTracks: () => [{ stop: jest.fn() }]
      })
    }
  });

  // 模拟MediaRecorder
  global.MediaRecorder = jest.fn().mockImplementation(() => ({
    start: jest.fn(),
    stop: jest.fn(),
    ondataavailable: null,
    onstop: null,
    state: 'inactive'
  }));
};

describe('前端集成测试', () => {
  beforeAll(() => {
    server.listen();
    mockMediaDevices();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('语音识别功能集成测试', () => {
    test('语音识别演示页面完整流程', async () => {
      const user = userEvent.setup();
      renderWithProviders(<RecognitionDemo />);

      // 检查页面标题
      expect(screen.getByText('多模态AI识别演示')).toBeInTheDocument();

      // 检查语音识别选项卡
      expect(screen.getByText('语音识别')).toBeInTheDocument();

      // 模拟文件上传
      const fileInput = screen.getByLabelText(/拖拽音频文件/i);
      const audioFile = createMockFile('test.wav', 'audio/wav');

      await user.upload(fileInput, audioFile);

      // 点击开始识别按钮
      const recognizeButton = screen.getByText('开始识别');
      await user.click(recognizeButton);

      // 等待识别结果
      await waitFor(() => {
        expect(screen.getByText(/识别结果/i)).toBeInTheDocument();
      });

      // 验证结果显示
      await waitFor(() => {
        expect(screen.getByText(/这是一段测试语音识别结果/i)).toBeInTheDocument();
      });
    });

    test('实时语音录制功能', async () => {
      const user = userEvent.setup();
      renderWithProviders(<RecognitionDemo />);

      // 点击录音按钮
      const recordButton = screen.getByRole('button', { name: /点击开始录音/i });
      await user.click(recordButton);

      // 验证录音状态
      expect(screen.getByText(/点击停止录音/i)).toBeInTheDocument();

      // 停止录音
      await user.click(recordButton);

      // 验证录音完成
      expect(screen.getByText(/播放录音/i)).toBeInTheDocument();
    });
  });

  describe('智能合同管理集成测试', () => {
    test('语音合同录入完整流程', async () => {
      const user = userEvent.setup();
      renderWithProviders(<VoiceContractRecorder />);

      // 检查页面标题
      expect(screen.getByText('智能语音合同录入')).toBeInTheDocument();

      // 检查录制指南
      expect(screen.getByText('客户基本信息')).toBeInTheDocument();
      expect(screen.getByText('合同类型')).toBeInTheDocument();

      // 模拟录音
      const recordButton = screen.getByRole('button', { name: /点击开始录制/i });
      await user.click(recordButton);

      // 停止录音
      await user.click(recordButton);

      // 点击开始识别
      const processButton = screen.getByText('开始识别');
      await user.click(processButton);

      // 等待处理结果
      await waitFor(() => {
        expect(screen.getByText('提取的合同信息')).toBeInTheDocument();
      });

      // 验证合同信息显示
      expect(screen.getByText('北京科技公司')).toBeInTheDocument();
      expect(screen.getByText('张经理')).toBeInTheDocument();

      // 验证风险评估
      expect(screen.getByText(/风险等级.*LOW/i)).toBeInTheDocument();
    });

    test('合同列表管理功能', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ContractList />);

      // 检查页面标题
      expect(screen.getByText('合同管理')).toBeInTheDocument();

      // 等待合同列表加载
      await waitFor(() => {
        expect(screen.getByText('CT20240828001')).toBeInTheDocument();
      });

      // 验证合同信息显示
      expect(screen.getByText('北京科技公司')).toBeInTheDocument();
      expect(screen.getByText('¥100,000')).toBeInTheDocument();

      // 测试搜索功能
      const searchInput = screen.getByPlaceholderText('搜索客户名称...');
      await user.type(searchInput, '北京');

      // 测试状态筛选
      const statusFilter = screen.getByLabelText('状态筛选');
      await user.click(statusFilter);
      await user.click(screen.getByText('草稿'));
    });
  });

  describe('工作流设计器集成测试', () => {
    test('工作流设计和执行', async () => {
      const user = userEvent.setup();
      renderWithProviders(<WorkflowDesigner />);

      // 检查页面标题
      expect(screen.getByText(/工作流设计器/i)).toBeInTheDocument();

      // 检查节点库
      expect(screen.getByText('节点库')).toBeInTheDocument();
      expect(screen.getByText('输入节点')).toBeInTheDocument();
      expect(screen.getByText('处理节点')).toBeInTheDocument();

      // 测试保存工作流
      const saveButton = screen.getByText('保存');
      await user.click(saveButton);

      // 由于没有节点，应该显示提示
      // 这里可以添加更多的交互测试
    });
  });

  describe('应用整体集成测试', () => {
    test('应用启动和导航', async () => {
      const user = userEvent.setup();
      renderWithProviders(<App />);

      // 检查应用是否正常启动
      // 这里需要根据实际的App组件结构进行调整
      expect(document.body).toBeInTheDocument();
    });

    test('错误处理和用户反馈', async () => {
      // 模拟API错误
      server.use(
        rest.post('/api/v1/recognition/speech/recognize', (req, res, ctx) => {
          return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
        })
      );

      const user = userEvent.setup();
      renderWithProviders(<RecognitionDemo />);

      // 尝试进行语音识别
      const fileInput = screen.getByLabelText(/拖拽音频文件/i);
      const audioFile = createMockFile('test.wav', 'audio/wav');

      await user.upload(fileInput, audioFile);

      const recognizeButton = screen.getByText('开始识别');
      await user.click(recognizeButton);

      // 验证错误处理
      await waitFor(() => {
        // 这里应该显示错误信息
        // 具体的错误处理方式需要根据实际实现调整
      });
    });

    test('响应式设计测试', () => {
      // 测试不同屏幕尺寸下的布局
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });

      renderWithProviders(<RecognitionDemo />);

      // 验证移动端布局
      // 这里可以添加具体的响应式测试
    });

    test('性能测试', async () => {
      const startTime = performance.now();
      
      renderWithProviders(<RecognitionDemo />);
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // 验证渲染时间合理（小于1秒）
      expect(renderTime).toBeLessThan(1000);
    });
  });

  describe('数据流集成测试', () => {
    test('Redux状态管理', async () => {
      const user = userEvent.setup();
      renderWithProviders(<RecognitionDemo />);

      // 测试状态更新
      // 这里需要根据实际的Redux状态结构进行测试
    });

    test('API数据流', async () => {
      const user = userEvent.setup();
      renderWithProviders(<ContractList />);

      // 等待API数据加载
      await waitFor(() => {
        expect(screen.getByText('CT20240828001')).toBeInTheDocument();
      });

      // 验证数据正确显示
      expect(screen.getByText('北京科技公司')).toBeInTheDocument();
    });
  });
});
