{"version": 3, "file": "category.js", "sourceRoot": "", "sources": ["../../../src/ui/legend/category.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAEvC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAa,UAAU,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAEhF,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AACzC,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,wBAAwB,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAKnE;IAA8B,4BAA6B;IACzD,kBAAY,OAAwB;QAClC,OAAA,MAAK,YAAC,OAAO,EAAE,wBAAwB,CAAC,SAAC;IAC3C,CAAC;IAUO,8BAAW,GAAnB,UAAoB,SAAoB,EAAE,KAAa,EAAE,MAAc;QAC/D,IAAA,KAA2B,IAAI,CAAC,UAAU,EAAxC,SAAS,eAAA,EAAE,SAAS,eAAoB,CAAC;QACjD,IAAM,KAAK,GAAG,aAAa,CAAkB,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACjE,IAAA,KAAA,OAA2B,UAAU,CAAC,KAAK,CAAC,IAAA,EAA3C,UAAU,QAAA,EAAE,UAAU,QAAqB,CAAC;QAEnD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,sBAAsB,CAAQ,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAE1G,IAAM,eAAe,uBAAK,KAAK,OAAA,EAAE,MAAM,QAAA,IAAK,UAAU,KAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,GAAE,CAAC;QAC3F,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU;aACzB,sBAAsB,CAAC,WAAW,CAAC,KAAK,EAAE,cAAM,OAAA,IAAI,KAAK,CAAC,EAAE,KAAK,EAAE,eAAkC,EAAE,CAAC,EAAxD,CAAwD,CAAC;aACzG,MAAM,CAAC,eAAe,CAAqB,CAAC;IACjD,CAAC;IAEO,8BAAW,GAAnB,UAAoB,SAAoB,EAAE,IAAa;QAC7C,IAAA,CAAC,GAAuB,IAAI,EAA3B,EAAE,CAAC,GAAoB,IAAI,EAAxB,EAAE,KAAK,GAAa,IAAI,MAAjB,EAAE,MAAM,GAAK,IAAI,OAAT,CAAU;QACrC,IAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACtD,IAAA,KAAA,OAAiC,UAAU,CAAC,KAAK,CAAC,IAAA,EAAjD,gBAAgB,QAAA,EAAE,UAAU,QAAqB,CAAC;QACzD,gDAAgD;QAChD,yBAAyB;QACzB,IAAM,SAAS,GAAG,sBAAK,gBAAgB,KAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAwB,CAAC;QAC3F,IAAI,CAAC,UAAU,GAAG,SAAS;aACxB,sBAAsB,CAAQ,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC;aAC1D,MAAM,uBAAM,UAAU,KAAE,SAAS,EAAE,oBAAa,CAAC,eAAK,CAAC,MAAG,IAAG,CAAC;QACjE,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,UAAU;aACZ,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC;aAClC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;aACf,IAAI,CACH,UAAC,KAAK;YACJ,OAAA,KAAK;iBACF,MAAM,CAAC,cAAM,OAAA,IAAI,aAAa,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,EAAvC,CAAuC,CAAC;iBACrD,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;iBACzC,IAAI,CAAC;gBACJ,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC,CAAC;QALJ,CAKI,EACN,UAAC,MAAM,IAAK,OAAA,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,EAAxB,CAAwB,EACpC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,EAAE,EAAb,CAAa,CACxB,CAAC;IACN,CAAC;IAEO,+BAAY,GAApB;QACU,IAAA,SAAS,GAAK,IAAI,CAAC,UAAU,UAApB,CAAqB;QACtC,IAAI,SAAS,EAAE,CAAC;YACR,IAAA,KAAW,IAAI,CAAC,KAAK,CAAC,IAAI,EAAS,CAAC,iBAAiB,EAAE,EAArD,CAAC,OAAA,EAAE,CAAC,OAAiD,CAAC;YAC9D,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,oBAAa,CAAC,eAAK,CAAC,MAAG,CAAC;QACnE,CAAC;IACH,CAAC;IAED,sBAAY,oCAAc;aAA1B;YACQ,IAAA,KAA+B,IAAI,CAAC,UAAU,EAA5C,SAAS,eAAA,EAAE,KAAK,WAAA,EAAE,MAAM,YAAoB,CAAC;YACrD,IAAI,CAAC,SAAS;gBAAE,OAAO,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAM,EAAE,MAAO,CAAC,CAAC;YACvD,OAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,EAAY,CAAC,iBAAiB,EAAE,CAAC;QAC1D,CAAC;;;OAAA;IAEM,0BAAO,GAAd;;QACE,IAAM,KAAK,GAAU,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,EAAE,CAAC;QACxC,IAAM,KAAK,GAAG,MAAA,IAAI,CAAC,KAAK,0CAAE,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK;YAAE,OAAO,gBAAK,CAAC,OAAO,WAAE,CAAC;QAC7C,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,yBAAM,GAAN,UAAO,UAAwC,EAAE,SAAgB;QACzD,IAAA,KAAkC,IAAI,CAAC,UAAU,EAA/C,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,SAAK,EAAL,CAAC,mBAAG,CAAC,KAAA,EAAE,SAAK,EAAL,CAAC,mBAAG,CAAC,KAAoB,CAAC;QACxD,IAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9B,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,oBAAa,CAAC,eAAK,CAAC,MAAG,CAAC;QAEpD,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAM,EAAE,MAAO,CAAC,CAAC;QAEvC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAE3C,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IACH,eAAC;AAAD,CAAC,AArFD,CAA8B,SAAS,GAqFtC"}