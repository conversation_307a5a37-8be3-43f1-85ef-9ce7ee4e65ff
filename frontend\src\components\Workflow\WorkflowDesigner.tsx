import React, { useState, useCallback, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Drawer,
  Toolbar,
  AppBar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  Save,
  Add,
  Delete,
  Edit,
  Settings,
  DragIndicator,
  ExpandMore,
  Input,
  Transform,
  Visibility,
  Decision,
  Action,
  Output,
  CallSplit,
  Loop,
  Close
} from '@mui/icons-material';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  ReactFlowProvider,
  NodeTypes,
  EdgeTypes
} from 'reactflow';
import 'reactflow/dist/style.css';

// 自定义节点类型
const CustomNode = ({ data, selected }: any) => {
  const getNodeIcon = (type: string) => {
    switch (type) {
      case 'input': return <Input />;
      case 'processing': return <Transform />;
      case 'recognition': return <Visibility />;
      case 'decision': return <Decision />;
      case 'action': return <Action />;
      case 'output': return <Output />;
      case 'parallel': return <CallSplit />;
      case 'loop': return <Loop />;
      default: return <Transform />;
    }
  };

  const getNodeColor = (type: string) => {
    switch (type) {
      case 'input': return '#4CAF50';
      case 'processing': return '#2196F3';
      case 'recognition': return '#FF9800';
      case 'decision': return '#9C27B0';
      case 'action': return '#F44336';
      case 'output': return '#607D8B';
      case 'parallel': return '#795548';
      case 'loop': return '#3F51B5';
      default: return '#757575';
    }
  };

  return (
    <Card 
      sx={{ 
        minWidth: 150,
        border: selected ? '2px solid #1976d2' : '1px solid #e0e0e0',
        backgroundColor: selected ? '#f3f4f6' : 'white'
      }}
    >
      <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Box sx={{ color: getNodeColor(data.type), mr: 1 }}>
            {getNodeIcon(data.type)}
          </Box>
          <Typography variant="subtitle2" fontWeight="bold">
            {data.label}
          </Typography>
        </Box>
        <Typography variant="caption" color="textSecondary">
          {data.type}
        </Typography>
        {data.description && (
          <Typography variant="body2" sx={{ mt: 1, fontSize: '0.75rem' }}>
            {data.description}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

const nodeTypes: NodeTypes = {
  custom: CustomNode,
};

interface WorkflowStep {
  id: string;
  type: string;
  name: string;
  description?: string;
  config?: any;
  position: { x: number; y: number };
}

const WorkflowDesigner: React.FC = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [drawerOpen, setDrawerOpen] = useState(true);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [configDialog, setConfigDialog] = useState(false);
  const [nodeConfig, setNodeConfig] = useState<any>({});
  const [workflowName, setWorkflowName] = useState('新工作流');
  const [isExecuting, setIsExecuting] = useState(false);

  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null);

  // 节点模板
  const nodeTemplates = [
    { type: 'input', label: '输入节点', icon: <Input />, description: '接收输入数据' },
    { type: 'processing', label: '处理节点', icon: <Transform />, description: '数据处理和转换' },
    { type: 'recognition', label: '识别节点', icon: <Visibility />, description: '多模态识别' },
    { type: 'decision', label: '决策节点', icon: <Decision />, description: '条件判断' },
    { type: 'action', label: '动作节点', icon: <Action />, description: '执行操作' },
    { type: 'output', label: '输出节点', icon: <Output />, description: '输出结果' },
    { type: 'parallel', label: '并行节点', icon: <CallSplit />, description: '并行执行' },
    { type: 'loop', label: '循环节点', icon: <Loop />, description: '循环处理' },
  ];

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');

      if (typeof type === 'undefined' || !type || !reactFlowBounds) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const template = nodeTemplates.find(t => t.type === type);
      const newNode: Node = {
        id: `${type}_${Date.now()}`,
        type: 'custom',
        position,
        data: {
          label: template?.label || type,
          type: type,
          description: template?.description,
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes, nodeTemplates]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
  }, []);

  const onNodeDoubleClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
    setNodeConfig(node.data.config || {});
    setConfigDialog(true);
  }, []);

  const handleNodeConfigSave = () => {
    if (selectedNode) {
      setNodes((nds) =>
        nds.map((node) =>
          node.id === selectedNode.id
            ? {
                ...node,
                data: {
                  ...node.data,
                  config: nodeConfig,
                },
              }
            : node
        )
      );
    }
    setConfigDialog(false);
  };

  const deleteSelectedNode = () => {
    if (selectedNode) {
      setNodes((nds) => nds.filter((node) => node.id !== selectedNode.id));
      setEdges((eds) => eds.filter((edge) => 
        edge.source !== selectedNode.id && edge.target !== selectedNode.id
      ));
      setSelectedNode(null);
    }
  };

  const saveWorkflow = async () => {
    const workflowData = {
      name: workflowName,
      nodes: nodes.map(node => ({
        id: node.id,
        type: node.data.type,
        name: node.data.label,
        description: node.data.description,
        config: node.data.config || {},
        position: node.position,
      })),
      edges: edges.map(edge => ({
        source: edge.source,
        target: edge.target,
        type: edge.type || 'default',
      })),
    };

    try {
      // 这里应该调用API保存工作流
      console.log('Saving workflow:', workflowData);
      alert('工作流保存成功！');
    } catch (error) {
      console.error('Failed to save workflow:', error);
      alert('工作流保存失败');
    }
  };

  const executeWorkflow = async () => {
    if (nodes.length === 0) {
      alert('请先添加工作流节点');
      return;
    }

    setIsExecuting(true);
    try {
      // 这里应该调用API执行工作流
      console.log('Executing workflow...');
      await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟执行
      alert('工作流执行完成！');
    } catch (error) {
      console.error('Failed to execute workflow:', error);
      alert('工作流执行失败');
    } finally {
      setIsExecuting(false);
    }
  };

  const onDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };

  const renderNodeConfig = () => {
    if (!selectedNode) return null;

    const nodeType = selectedNode.data.type;

    switch (nodeType) {
      case 'recognition':
        return (
          <Box>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>识别类型</InputLabel>
              <Select
                value={nodeConfig.recognitionType || ''}
                label="识别类型"
                onChange={(e) => setNodeConfig(prev => ({ ...prev, recognitionType: e.target.value }))}
              >
                <MenuItem value="speech">语音识别</MenuItem>
                <MenuItem value="vision">视觉识别</MenuItem>
                <MenuItem value="document">文档识别</MenuItem>
                <MenuItem value="biometric">生物特征识别</MenuItem>
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="配置参数"
              multiline
              rows={3}
              value={JSON.stringify(nodeConfig.config || {}, null, 2)}
              onChange={(e) => {
                try {
                  const config = JSON.parse(e.target.value);
                  setNodeConfig(prev => ({ ...prev, config }));
                } catch (error) {
                  // 忽略JSON解析错误
                }
              }}
              sx={{ mb: 2 }}
            />
          </Box>
        );

      case 'decision':
        return (
          <Box>
            <TextField
              fullWidth
              label="判断条件"
              value={nodeConfig.condition || ''}
              onChange={(e) => setNodeConfig(prev => ({ ...prev, condition: e.target.value }))}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="真值动作"
              value={nodeConfig.trueAction || ''}
              onChange={(e) => setNodeConfig(prev => ({ ...prev, trueAction: e.target.value }))}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="假值动作"
              value={nodeConfig.falseAction || ''}
              onChange={(e) => setNodeConfig(prev => ({ ...prev, falseAction: e.target.value }))}
              sx={{ mb: 2 }}
            />
          </Box>
        );

      case 'action':
        return (
          <Box>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>动作类型</InputLabel>
              <Select
                value={nodeConfig.actionType || ''}
                label="动作类型"
                onChange={(e) => setNodeConfig(prev => ({ ...prev, actionType: e.target.value }))}
              >
                <MenuItem value="api_call">API调用</MenuItem>
                <MenuItem value="send_email">发送邮件</MenuItem>
                <MenuItem value="save_data">保存数据</MenuItem>
                <MenuItem value="generate_contract">生成合同</MenuItem>
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="动作配置"
              multiline
              rows={3}
              value={JSON.stringify(nodeConfig.actionConfig || {}, null, 2)}
              onChange={(e) => {
                try {
                  const actionConfig = JSON.parse(e.target.value);
                  setNodeConfig(prev => ({ ...prev, actionConfig }));
                } catch (error) {
                  // 忽略JSON解析错误
                }
              }}
              sx={{ mb: 2 }}
            />
          </Box>
        );

      default:
        return (
          <Box>
            <TextField
              fullWidth
              label="节点名称"
              value={nodeConfig.name || selectedNode.data.label}
              onChange={(e) => setNodeConfig(prev => ({ ...prev, name: e.target.value }))}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="节点描述"
              multiline
              rows={2}
              value={nodeConfig.description || ''}
              onChange={(e) => setNodeConfig(prev => ({ ...prev, description: e.target.value }))}
              sx={{ mb: 2 }}
            />
          </Box>
        );
    }
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex' }}>
      {/* 工具栏 */}
      <AppBar position="static" color="default" elevation={1}>
        <Toolbar>
          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            工作流设计器 - {workflowName}
          </Typography>
          <Button
            variant="outlined"
            startIcon={<Save />}
            onClick={saveWorkflow}
            sx={{ mr: 1 }}
          >
            保存
          </Button>
          <Button
            variant="contained"
            startIcon={isExecuting ? <Stop /> : <PlayArrow />}
            onClick={executeWorkflow}
            disabled={isExecuting}
            color={isExecuting ? "secondary" : "primary"}
          >
            {isExecuting ? '执行中...' : '执行'}
          </Button>
        </Toolbar>
      </AppBar>

      {/* 侧边栏 */}
      <Drawer
        variant="persistent"
        anchor="left"
        open={drawerOpen}
        sx={{
          width: 280,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 280,
            boxSizing: 'border-box',
            position: 'relative',
          },
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            节点库
          </Typography>
          <List>
            {nodeTemplates.map((template) => (
              <ListItem
                key={template.type}
                sx={{
                  border: '1px solid #e0e0e0',
                  borderRadius: 1,
                  mb: 1,
                  cursor: 'grab',
                  '&:hover': {
                    backgroundColor: 'action.hover',
                  },
                }}
                draggable
                onDragStart={(event) => onDragStart(event, template.type)}
              >
                <ListItemIcon>{template.icon}</ListItemIcon>
                <ListItemText
                  primary={template.label}
                  secondary={template.description}
                />
                <DragIndicator />
              </ListItem>
            ))}
          </List>

          {selectedNode && (
            <Box sx={{ mt: 3 }}>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                节点属性
              </Typography>
              <Typography variant="body2" gutterBottom>
                {selectedNode.data.label}
              </Typography>
              <Button
                variant="outlined"
                startIcon={<Settings />}
                onClick={() => {
                  setNodeConfig(selectedNode.data.config || {});
                  setConfigDialog(true);
                }}
                sx={{ mr: 1, mb: 1 }}
              >
                配置
              </Button>
              <Button
                variant="outlined"
                startIcon={<Delete />}
                onClick={deleteSelectedNode}
                color="error"
              >
                删除
              </Button>
            </Box>
          )}
        </Box>
      </Drawer>

      {/* 主工作区 */}
      <Box sx={{ flexGrow: 1, height: 'calc(100vh - 64px)' }}>
        <ReactFlowProvider>
          <div ref={reactFlowWrapper} style={{ width: '100%', height: '100%' }}>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onInit={setReactFlowInstance}
              onDrop={onDrop}
              onDragOver={onDragOver}
              onNodeClick={onNodeClick}
              onNodeDoubleClick={onNodeDoubleClick}
              nodeTypes={nodeTypes}
              fitView
            >
              <Controls />
              <MiniMap />
              <Background variant="dots" gap={12} size={1} />
            </ReactFlow>
          </div>
        </ReactFlowProvider>
      </Box>

      {/* 节点配置对话框 */}
      <Dialog open={configDialog} onClose={() => setConfigDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          节点配置 - {selectedNode?.data.label}
        </DialogTitle>
        <DialogContent>
          {renderNodeConfig()}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfigDialog(false)}>取消</Button>
          <Button onClick={handleNodeConfigSave} variant="contained">保存</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WorkflowDesigner;
