{"name": "@antv/component", "version": "2.1.6", "description": "Visualization components for AntV, based on G.", "license": "MIT", "main": "lib/index.js", "module": "esm/index.js", "unpkg": "dist/component.min.js", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "import": "./esm/index.js", "require": "./lib/index.js"}}, "files": ["src", "lib", "esm", "dist"], "sideEffects": false, "scripts": {"dev": "vite", "clean": "<PERSON><PERSON>f lib esm dist", "lint-staged": "lint-staged", "size": "limit-size", "lint": "NODE_ENV=test && eslint ./src/**/*.ts ./__tests__/**/*.ts && prettier ./src ./__tests__ --check ", "fix": "eslint ./src/**/*.ts ./__tests__/**/*.ts --fix && prettier ./src ./__tests__ --write ", "test": "jest", "build": "run-s clean build:* size", "build:umd": "webpack", "build:cjs": "tsc -p tsconfig.json --target ES5 --module commonjs --outDir lib", "build:esm": "tsc -p tsconfig.json --target ES5 --module ESNext --outDir esm", "ci": "run-s lint test build", "prepublishOnly": "npm run ci", "prepare": "husky install"}, "keywords": ["antv", "visualization", "ui", "component"], "dependencies": {"@antv/g": "^6.1.11", "@antv/scale": "^0.4.16", "@antv/util": "^3.3.10", "svg-path-parser": "^1.1.0"}, "devDependencies": {"@antv/g-canvas": "^2.0.29", "@antv/g-plugin-control": "^2.0.22", "@antv/g-svg": "^2.0.24", "@antv/g-webgl": "^2.0.33", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-transform-runtime": "^7.25.9", "@babel/preset-env": "^7.26.0", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^12.1.4", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/prettier": "^2.7.3", "@types/react": "^18.0.0", "@types/react-dom": "^18.3.1", "@types/svg-path-parser": "^1.1.6", "@types/xmlserializer": "^0.6.6", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^2.2.0", "antd": "^5.22.2", "archiver": "^5.3.2", "babel-loader": "^8.4.1", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^4.2.1", "gh-pages": "^3.2.3", "husky": "^5.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^19.0.0", "limit-size": "^0.1.4", "lint-staged": "^10.5.4", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^11.18.6", "rimraf": "^3.0.2", "svgo": "^3.3.2", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "vite": "^3.2.11", "webpack": "^5.96.1", "webpack-cli": "^5.1.4", "xmlserializer": "^0.6.1"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write", "git add"]}, "limit-size": [{"path": "dist/component.min.js", "limit": "80 Kb", "gzip": true}, {"path": "dist/component.min.js", "limit": "250 Kb"}], "author": {"name": "AntV", "url": "https://antv.vision/"}, "repository": {"type": "git", "url": "https://github.com/antvis/component"}, "bugs": {"url": "https://github.com/antvis/component/issues"}}