"""
多模态识别API端点
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.models.recognition import RecognitionTask, RecognitionResult, RecognitionType
from app.schemas.recognition import (
    RecognitionRequest,
    RecognitionResponse,
    SpeechRecognitionRequest,
    VisionRecognitionRequest,
    DocumentRecognitionRequest,
    BiometricRecognitionRequest,
    BatchRecognitionRequest,
    BatchRecognitionResponse,
    RecognitionTaskResponse,
    RecognitionHistoryResponse,
    RecognitionStatistics,
    ModelPerformanceMetrics,
    RecognitionHealthCheck
)
from app.services.recognition_service import RecognitionService
import logging
import base64
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/recognize", response_model=RecognitionResponse)
async def recognize(
    request: RecognitionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """通用识别接口"""
    try:
        service = RecognitionService(db)
        
        # 生成任务ID（如果未提供）
        if not request.task_id:
            request.task_id = f"task_{uuid.uuid4().hex[:12]}"
        
        # 处理识别请求
        result = await service.process_recognition(request.dict())
        
        return RecognitionResponse(**result)
        
    except Exception as e:
        logger.error(f"Recognition failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Recognition processing failed: {str(e)}"
        )


@router.post("/speech/recognize", response_model=RecognitionResponse)
async def speech_recognize(
    request: SpeechRecognitionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """语音识别接口"""
    try:
        service = RecognitionService(db)
        
        # 构建识别请求
        recognition_request = {
            "task_id": f"speech_{uuid.uuid4().hex[:12]}",
            "recognition_type": "speech",
            "input_data": request.dict(),
            "config": {
                "language": request.language,
                "enable_emotion": request.enable_emotion,
                "enable_speaker_id": request.enable_speaker_id,
                "enable_timestamps": request.enable_timestamps
            }
        }
        
        result = await service.process_recognition(recognition_request)
        return RecognitionResponse(**result)
        
    except Exception as e:
        logger.error(f"Speech recognition failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Speech recognition failed: {str(e)}"
        )


@router.post("/vision/analyze", response_model=RecognitionResponse)
async def vision_analyze(
    request: VisionRecognitionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """视觉识别接口"""
    try:
        service = RecognitionService(db)
        
        recognition_request = {
            "task_id": f"vision_{uuid.uuid4().hex[:12]}",
            "recognition_type": "vision",
            "input_data": request.dict(),
            "config": {
                "enable_classification": request.enable_classification,
                "enable_detection": request.enable_detection,
                "enable_face_detection": request.enable_face_detection,
                "enable_ocr": request.enable_ocr,
                "detection_threshold": request.detection_threshold
            }
        }
        
        result = await service.process_recognition(recognition_request)
        return RecognitionResponse(**result)
        
    except Exception as e:
        logger.error(f"Vision recognition failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Vision recognition failed: {str(e)}"
        )


@router.post("/document/extract", response_model=RecognitionResponse)
async def document_extract(
    request: DocumentRecognitionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """文档识别接口"""
    try:
        service = RecognitionService(db)
        
        recognition_request = {
            "task_id": f"document_{uuid.uuid4().hex[:12]}",
            "recognition_type": "document",
            "input_data": request.dict(),
            "config": {
                "document_type": request.document_type,
                "enable_table_extraction": request.enable_table_extraction,
                "enable_seal_detection": request.enable_seal_detection,
                "language": request.language,
                "output_format": request.output_format
            }
        }
        
        result = await service.process_recognition(recognition_request)
        return RecognitionResponse(**result)
        
    except Exception as e:
        logger.error(f"Document recognition failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Document recognition failed: {str(e)}"
        )


@router.post("/biometric/identify", response_model=RecognitionResponse)
async def biometric_identify(
    request: BiometricRecognitionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """生物特征识别接口"""
    try:
        service = RecognitionService(db)
        
        recognition_request = {
            "task_id": f"biometric_{uuid.uuid4().hex[:12]}",
            "recognition_type": "biometric",
            "input_data": request.dict(),
            "config": {
                "biometric_type": request.biometric_type,
                "operation": request.operation,
                "template_id": request.template_id,
                "quality_threshold": request.quality_threshold,
                "match_threshold": request.match_threshold
            }
        }
        
        result = await service.process_recognition(recognition_request)
        return RecognitionResponse(**result)
        
    except Exception as e:
        logger.error(f"Biometric recognition failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Biometric recognition failed: {str(e)}"
        )


@router.post("/upload", response_model=RecognitionResponse)
async def upload_and_recognize(
    file: UploadFile = File(...),
    recognition_type: RecognitionType = Query(..., description="识别类型"),
    config: Optional[str] = Query(None, description="配置参数（JSON字符串）"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """文件上传识别接口"""
    try:
        # 读取文件内容
        file_content = await file.read()
        file_base64 = base64.b64encode(file_content).decode('utf-8')
        
        # 构建输入数据
        input_data = {}
        if recognition_type == RecognitionType.SPEECH:
            input_data["audio_base64"] = file_base64
            input_data["format"] = file.filename.split('.')[-1] if '.' in file.filename else "wav"
        elif recognition_type == RecognitionType.VISION:
            input_data["image_base64"] = file_base64
            input_data["format"] = file.filename.split('.')[-1] if '.' in file.filename else "jpg"
        elif recognition_type == RecognitionType.DOCUMENT:
            input_data["document_base64"] = file_base64
            input_data["format"] = file.filename.split('.')[-1] if '.' in file.filename else "pdf"
        elif recognition_type == RecognitionType.BIOMETRIC:
            input_data["data_base64"] = file_base64
            input_data["format"] = file.filename.split('.')[-1] if '.' in file.filename else "jpg"
        
        # 解析配置
        import json
        config_dict = json.loads(config) if config else {}
        
        service = RecognitionService(db)
        recognition_request = {
            "task_id": f"upload_{uuid.uuid4().hex[:12]}",
            "recognition_type": recognition_type.value,
            "input_data": input_data,
            "config": config_dict
        }
        
        result = await service.process_recognition(recognition_request)
        return RecognitionResponse(**result)
        
    except Exception as e:
        logger.error(f"Upload recognition failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Upload recognition failed: {str(e)}"
        )


@router.post("/batch", response_model=BatchRecognitionResponse)
async def batch_recognize(
    request: BatchRecognitionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量识别接口"""
    try:
        service = RecognitionService(db)
        batch_id = f"batch_{uuid.uuid4().hex[:12]}"
        
        # 这里应该实现批量处理逻辑
        # 目前返回模拟响应
        return BatchRecognitionResponse(
            batch_id=batch_id,
            batch_name=request.batch_name,
            total_tasks=len(request.tasks),
            status="processing",
            created_at=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"Batch recognition failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Batch recognition failed: {str(e)}"
        )


@router.get("/tasks/{task_id}", response_model=RecognitionTaskResponse)
async def get_task(
    task_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取识别任务详情"""
    task = db.query(RecognitionTask).filter(RecognitionTask.task_id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    
    return RecognitionTaskResponse.from_orm(task)


@router.get("/history", response_model=RecognitionHistoryResponse)
async def get_recognition_history(
    recognition_type: Optional[RecognitionType] = Query(None, description="识别类型过滤"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="页大小"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取识别历史记录"""
    try:
        service = RecognitionService(db)
        
        # 获取历史记录
        history = await service.get_recognition_history(
            user_id=current_user.id,
            recognition_type=recognition_type,
            limit=size
        )
        
        # 构建统计信息
        total_tasks = len(history)
        completed_tasks = len([h for h in history if h.status == "completed"])
        failed_tasks = len([h for h in history if h.status == "failed"])
        
        statistics = RecognitionStatistics(
            total_tasks=total_tasks,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks,
            success_rate=completed_tasks / total_tasks * 100 if total_tasks > 0 else 0,
            tasks_by_type={},
            tasks_by_status={}
        )
        
        return RecognitionHistoryResponse(
            tasks=[RecognitionTaskResponse.from_orm(task) for task in history],
            total=total_tasks,
            page=page,
            size=size,
            statistics=statistics
        )
        
    except Exception as e:
        logger.error(f"Get recognition history failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Get recognition history failed: {str(e)}"
        )


@router.get("/statistics", response_model=RecognitionStatistics)
async def get_recognition_statistics(
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    recognition_type: Optional[RecognitionType] = Query(None, description="识别类型"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取识别统计信息"""
    try:
        # 这里应该实现统计逻辑
        # 目前返回模拟数据
        return RecognitionStatistics(
            total_tasks=100,
            completed_tasks=85,
            failed_tasks=15,
            average_confidence=0.92,
            average_processing_time=1.5,
            success_rate=85.0,
            tasks_by_type={
                "speech": 30,
                "vision": 25,
                "document": 25,
                "biometric": 20
            },
            tasks_by_status={
                "completed": 85,
                "failed": 15
            }
        )
        
    except Exception as e:
        logger.error(f"Get recognition statistics failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Get recognition statistics failed: {str(e)}"
        )


@router.get("/health", response_model=RecognitionHealthCheck)
async def health_check(
    db: Session = Depends(get_db)
):
    """识别服务健康检查"""
    try:
        return RecognitionHealthCheck(
            status="healthy",
            models_loaded={
                "speech": True,
                "vision": True,
                "document": True,
                "biometric": True
            },
            processing_queue_size=0,
            active_tasks=0,
            system_resources={
                "cpu_usage": 45.2,
                "memory_usage": 68.5,
                "gpu_usage": 32.1
            },
            last_check=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health check failed: {str(e)}"
        )
