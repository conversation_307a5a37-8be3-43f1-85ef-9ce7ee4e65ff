/**
 * Creates an object composed of the picked `object` properties.
 *
 * @param {Object} object The source object.
 * @param {...(string[])} [paths] The property paths to pick.
 * @returns {Object} Returns the new object.
 * @example
 *
 * var object = { 'a': 1, 'b': '2', 'c': 3 };
 * pick(object, ['a', 'c']);  // => { 'a': 1, 'c': 3 }
 */
export interface ObjectType<T> {
    [key: string]: T;
}
declare const _default: <T>(object: ObjectType<T>, keys: string[]) => ObjectType<T>;
export default _default;
